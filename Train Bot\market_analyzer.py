#!/usr/bin/env python3
"""
Market Analyzer Module for Trading Bot
Analyzes market structure, trends, support/resistance levels, and technical indicators
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from utils import print_colored

class MarketAnalyzer:
    def __init__(self):
        """Initialize market analyzer"""
        pass
    
    def analyze_market_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Comprehensive market structure analysis"""
        try:
            if len(df) < 20:
                return self._default_market_analysis()
            
            # Get trend analysis
            market_trend = self._determine_market_trend(df)
            short_trend = self._determine_short_trend(df)
            long_trend = self._determine_long_trend(df)
            
            # Get support and resistance levels
            support_level, resistance_level = self._find_support_resistance(df)
            
            # Analyze market structure
            market_structure = self._analyze_structure_pattern(df)
            
            # Check for breakouts
            breakout_detected = self._detect_breakout(df, support_level, resistance_level)
            
            # Analyze previous candles
            previous_candles_info = self._analyze_previous_candles(df)
            
            return {
                'market_trend': market_trend,
                'short_trend': short_trend,
                'long_trend': long_trend,
                'support_level': support_level,
                'resistance_level': resistance_level,
                'market_structure': market_structure,
                'breakout_detected': breakout_detected,
                'previous_candles_info': previous_candles_info
            }
            
        except Exception as e:
            print_colored(f"❌ Market analysis error: {e}", "ERROR")
            return self._default_market_analysis()
    
    def _determine_market_trend(self, df: pd.DataFrame) -> str:
        """Determine overall market trend"""
        try:
            # Use EMA crossover and price action
            if 'ema_12' in df.columns and 'ema_26' in df.columns:
                current_ema12 = df['ema_12'].iloc[-1]
                current_ema26 = df['ema_26'].iloc[-1]
                prev_ema12 = df['ema_12'].iloc[-5] if len(df) > 5 else current_ema12
                prev_ema26 = df['ema_26'].iloc[-5] if len(df) > 5 else current_ema26
                
                # Current trend
                if current_ema12 > current_ema26:
                    if prev_ema12 > prev_ema26:
                        return "bullish"
                    else:
                        return "turning_bullish"
                else:
                    if prev_ema12 < prev_ema26:
                        return "bearish"
                    else:
                        return "turning_bearish"
            
            # Fallback to price action
            recent_highs = df['high'].tail(10).max()
            recent_lows = df['low'].tail(10).min()
            current_price = df['close'].iloc[-1]
            
            if current_price > (recent_highs + recent_lows) / 2:
                return "bullish"
            else:
                return "bearish"
                
        except Exception:
            return "sideways"
    
    def _determine_short_trend(self, df: pd.DataFrame) -> str:
        """Determine short-term trend (last 5 candles)"""
        try:
            if len(df) < 5:
                return "sideways"
            
            recent_closes = df['close'].tail(5).values
            if len(recent_closes) < 2:
                return "sideways"
            
            # Calculate trend slope
            x = np.arange(len(recent_closes))
            slope = np.polyfit(x, recent_closes, 1)[0]
            
            if slope > 0.0001:
                return "bullish"
            elif slope < -0.0001:
                return "bearish"
            else:
                return "sideways"
                
        except Exception:
            return "sideways"
    
    def _determine_long_trend(self, df: pd.DataFrame) -> str:
        """Determine long-term trend (last 20 candles)"""
        try:
            if len(df) < 20:
                return "sideways"
            
            long_closes = df['close'].tail(20).values
            if len(long_closes) < 2:
                return "sideways"
            
            # Calculate trend slope
            x = np.arange(len(long_closes))
            slope = np.polyfit(x, long_closes, 1)[0]
            
            if slope > 0.0001:
                return "bullish"
            elif slope < -0.0001:
                return "bearish"
            else:
                return "sideways"
                
        except Exception:
            return "sideways"
    
    def _find_support_resistance(self, df: pd.DataFrame) -> Tuple[float, float]:
        """Find key support and resistance levels"""
        try:
            if len(df) < 20:
                current_price = df['close'].iloc[-1]
                return current_price * 0.999, current_price * 1.001
            
            # Get recent highs and lows
            recent_data = df.tail(20)
            
            # Find pivot points
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # Support: Recent significant low
            support_candidates = []
            for i in range(2, len(lows) - 2):
                if (lows[i] <= lows[i-1] and lows[i] <= lows[i-2] and 
                    lows[i] <= lows[i+1] and lows[i] <= lows[i+2]):
                    support_candidates.append(lows[i])
            
            # Resistance: Recent significant high
            resistance_candidates = []
            for i in range(2, len(highs) - 2):
                if (highs[i] >= highs[i-1] and highs[i] >= highs[i-2] and 
                    highs[i] >= highs[i+1] and highs[i] >= highs[i+2]):
                    resistance_candidates.append(highs[i])
            
            # Select most relevant levels
            current_price = df['close'].iloc[-1]
            
            if support_candidates:
                # Find support below current price
                valid_supports = [s for s in support_candidates if s < current_price]
                support_level = max(valid_supports) if valid_supports else min(support_candidates)
            else:
                support_level = recent_data['low'].min()
            
            if resistance_candidates:
                # Find resistance above current price
                valid_resistances = [r for r in resistance_candidates if r > current_price]
                resistance_level = min(valid_resistances) if valid_resistances else max(resistance_candidates)
            else:
                resistance_level = recent_data['high'].max()
            
            return support_level, resistance_level
            
        except Exception:
            current_price = df['close'].iloc[-1]
            return current_price * 0.999, current_price * 1.001
    
    def _analyze_structure_pattern(self, df: pd.DataFrame) -> str:
        """Analyze market structure pattern"""
        try:
            if len(df) < 10:
                return "consolidation"
            
            recent_data = df.tail(10)
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # Check for higher highs and higher lows (uptrend)
            higher_highs = sum(1 for i in range(1, len(highs)) if highs[i] > highs[i-1])
            higher_lows = sum(1 for i in range(1, len(lows)) if lows[i] > lows[i-1])
            
            # Check for lower highs and lower lows (downtrend)
            lower_highs = sum(1 for i in range(1, len(highs)) if highs[i] < highs[i-1])
            lower_lows = sum(1 for i in range(1, len(lows)) if lows[i] < lows[i-1])
            
            if higher_highs >= 6 and higher_lows >= 6:
                return "uptrend"
            elif lower_highs >= 6 and lower_lows >= 6:
                return "downtrend"
            elif higher_highs >= 4 or higher_lows >= 4:
                return "bullish_structure"
            elif lower_highs >= 4 or lower_lows >= 4:
                return "bearish_structure"
            else:
                return "consolidation"
                
        except Exception:
            return "consolidation"
    
    def _detect_breakout(self, df: pd.DataFrame, support: float, resistance: float) -> bool:
        """Detect if there's a recent breakout"""
        try:
            if len(df) < 3:
                return False
            
            current_price = df['close'].iloc[-1]
            prev_price = df['close'].iloc[-2]
            
            # Check for breakout above resistance
            if current_price > resistance and prev_price <= resistance:
                return True
            
            # Check for breakdown below support
            if current_price < support and prev_price >= support:
                return True
            
            return False
            
        except Exception:
            return False
    
    def _analyze_previous_candles(self, df: pd.DataFrame) -> str:
        """Analyze recent candle patterns"""
        try:
            if len(df) < 5:
                return "insufficient_data"
            
            recent_candles = df.tail(5)
            
            # Count bullish vs bearish candles
            bullish_count = sum(1 for _, candle in recent_candles.iterrows() 
                              if candle['close'] > candle['open'])
            bearish_count = sum(1 for _, candle in recent_candles.iterrows() 
                              if candle['close'] < candle['open'])
            
            # Analyze pattern
            if bullish_count >= 4:
                return "strong_bullish_momentum"
            elif bullish_count >= 3:
                return "bullish_momentum"
            elif bearish_count >= 4:
                return "strong_bearish_momentum"
            elif bearish_count >= 3:
                return "bearish_momentum"
            else:
                return "mixed_signals"
                
        except Exception:
            return "analysis_error"
    
    def get_indicator_values(self, df: pd.DataFrame) -> Dict[str, float]:
        """Extract current technical indicator values"""
        try:
            indicators = {}
            
            if len(df) == 0:
                return indicators
            
            current = df.iloc[-1]
            
            # Basic indicators
            if 'ema_12' in df.columns:
                indicators['ema_12'] = round(current['ema_12'], 5)
            if 'ema_26' in df.columns:
                indicators['ema_26'] = round(current['ema_26'], 5)
            if 'rsi' in df.columns:
                indicators['rsi'] = round(current['rsi'], 2)
            if 'macd' in df.columns:
                indicators['macd'] = round(current['macd'], 6)
            if 'macd_signal' in df.columns:
                indicators['macd_signal'] = round(current['macd_signal'], 6)
            
            # Bollinger Bands
            if 'bb_upper' in df.columns:
                indicators['bb_upper'] = round(current['bb_upper'], 5)
            if 'bb_middle' in df.columns:
                indicators['bb_middle'] = round(current['bb_middle'], 5)
            if 'bb_lower' in df.columns:
                indicators['bb_lower'] = round(current['bb_lower'], 5)
            
            # Stochastic
            if 'stoch_k' in df.columns:
                indicators['stoch_k'] = round(current['stoch_k'], 2)
            if 'stoch_d' in df.columns:
                indicators['stoch_d'] = round(current['stoch_d'], 2)
            
            # Volume
            if 'volume' in df.columns:
                indicators['volume'] = int(current['volume'])
            
            return indicators
            
        except Exception as e:
            print_colored(f"❌ Error extracting indicators: {e}", "ERROR")
            return {}
    
    def _default_market_analysis(self) -> Dict[str, Any]:
        """Return default market analysis when data is insufficient"""
        return {
            'market_trend': 'sideways',
            'short_trend': 'sideways',
            'long_trend': 'sideways',
            'support_level': 0.0,
            'resistance_level': 0.0,
            'market_structure': 'consolidation',
            'breakout_detected': False,
            'previous_candles_info': 'insufficient_data'
        }

# Global market analyzer instance
market_analyzer = MarketAnalyzer()

# Convenience functions
def analyze_market_structure(df: pd.DataFrame) -> Dict[str, Any]:
    """Analyze market structure using global analyzer"""
    return market_analyzer.analyze_market_structure(df)

def get_indicator_values(df: pd.DataFrame) -> Dict[str, float]:
    """Get indicator values using global analyzer"""
    return market_analyzer.get_indicator_values(df)
