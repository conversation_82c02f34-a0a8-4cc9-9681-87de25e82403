#!/usr/bin/env python3
"""
Comprehensive Strategy 5 Filter Test
Tests the updated Strategy 5 with market condition filter using August 8th data
Shows exact results: wins, losses, and filtered signals
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_engine import StrategyEngine
from utils import print_colored
from config import STRATEGY_5_FILTER_CONFIG

class Strategy5ComprehensiveTest:
    def __init__(self):
        """Initialize the test suite"""
        self.strategy_engine = StrategyEngine()
        self.strategy_engine.set_debug_mode(True)  # Enable debug mode for detailed output
        
    def load_signals_data(self, file_path):
        """Load signals data from JSON file"""
        try:
            with open(file_path, 'r') as f:
                signals = json.load(f)
            return signals
        except Exception as e:
            print_colored(f"❌ Error loading signals: {str(e)}", "ERROR")
            return None
    
    def create_dataframe_from_signal(self, signal):
        """Create a DataFrame from signal data for strategy testing"""
        try:
            # Extract indicators from signal
            indicators = signal.get('indicators', {})
            
            # Create a basic DataFrame with the signal data
            # Note: This is a simplified version - in real trading, you'd have full OHLCV data
            data = {
                'open': [signal['price'] * 0.9999],  # Simulate open price
                'high': [signal['price'] * 1.0001],  # Simulate high
                'low': [signal['price'] * 0.9999],   # Simulate low
                'close': [signal['price']],           # Current price
                'volume': [indicators.get('volume', 100)],
                'ema_12': [indicators.get('ema_12', signal['price'])],
                'ema_26': [indicators.get('ema_26', signal['price'])],
                'rsi': [indicators.get('rsi', 50)],
                'macd': [indicators.get('macd', 0)],
                'macd_signal': [indicators.get('macd_signal', 0)],
                'bb_upper': [indicators.get('bb_upper', signal['price'] * 1.001)],
                'bb_lower': [indicators.get('bb_lower', signal['price'] * 0.999)]
            }
            
            # Create DataFrame with multiple rows to simulate historical data
            df = pd.DataFrame(data)
            
            # Extend DataFrame to have enough historical data for strategy calculation
            for i in range(1, 50):  # Add 49 more historical rows
                row_data = {}
                for col in df.columns:
                    if col == 'close':
                        # Simulate price movement
                        row_data[col] = signal['price'] * (1 + np.random.normal(0, 0.001))
                    elif col == 'volume':
                        row_data[col] = indicators.get('volume', 100) * (1 + np.random.normal(0, 0.1))
                    else:
                        row_data[col] = df[col].iloc[0] * (1 + np.random.normal(0, 0.0005))
                
                df = pd.concat([pd.DataFrame([row_data]), df], ignore_index=True)
            
            return df
            
        except Exception as e:
            print_colored(f"❌ Error creating DataFrame: {str(e)}", "ERROR")
            return None
    
    def simulate_enhanced_filter_analysis(self, signal, df):
        """Simulate the enhanced multi-criteria filter analysis"""
        try:
            # Use the actual strategy engine's filter
            filter_result = self.strategy_engine._evaluate_strategy_5_filter_criteria(df)
            return filter_result

        except Exception as e:
            print_colored(f"❌ Error in filter analysis: {str(e)}", "ERROR")
            return {
                'should_execute': True,
                'score': 0.5,
                'min_score': 0.3,
                'reason': f"Error in analysis: {str(e)}"
            }
    
    def test_strategy_5_with_filter(self, signals_data):
        """Test Strategy 5 with the new market condition filter"""
        print_colored("\n🧪 Testing Strategy 5 with Market Condition Filter", "HEADER", bold=True)
        print_colored("=" * 70, "HEADER")
        
        # Filter only Strategy 5 signals
        s5_signals = [s for s in signals_data if s.get('strategy_used') == 'S5']
        
        if not s5_signals:
            print_colored("❌ No Strategy 5 signals found", "ERROR")
            return
        
        print_colored(f"📊 Found {len(s5_signals)} Strategy 5 signals to test", "INFO")
        
        # Test results
        filtered_signals = 0
        executed_signals = 0
        simulated_wins = 0
        simulated_losses = 0
        
        # Detailed results
        filter_details = []
        execution_details = []
        
        print_colored(f"\n🔍 Testing each signal with current filter settings:", "INFO")
        print_colored(f"   Filter Enabled: {self.strategy_engine.strategy_5_filter_enabled}", "INFO")
        print_colored(f"   Min Trend Strength: {self.strategy_engine.strategy_5_min_trend_strength}", "INFO")
        
        for i, signal in enumerate(s5_signals, 1):
            print_colored(f"\n--- Signal {i}/{len(s5_signals)} ---", "INFO")
            print_colored(f"Time: {signal['timestamp']}, Pair: {signal['pair']}, Direction: {signal['direction']}", "INFO")

            # Create DataFrame for analysis
            df = self.create_dataframe_from_signal(signal)
            if df is None:
                print_colored(f"❌ ERROR: Could not create test data", "ERROR")
                continue

            # Get enhanced filter analysis
            filter_result = self.simulate_enhanced_filter_analysis(signal, df)
            print_colored(f"Filter Analysis: {filter_result['reason']}", "INFO")
            print_colored(f"Score: {filter_result['score']:.3f} (min: {filter_result['min_score']:.3f})", "INFO")

            # Check if signal would be filtered
            if self.strategy_engine.strategy_5_filter_enabled and not filter_result['should_execute']:
                filtered_signals += 1
                filter_details.append({
                    'signal_num': i,
                    'time': signal['timestamp'],
                    'pair': signal['pair'],
                    'direction': signal['direction'],
                    'filter_score': filter_result['score'],
                    'min_score': filter_result['min_score'],
                    'original_result': signal.get('result', 'unknown'),
                    'reason': filter_result['reason']
                })
                print_colored(f"🚫 FILTERED: {filter_result['reason']}", "WARNING")
                print_colored(f"   Score {filter_result['score']:.3f} < {filter_result['min_score']:.3f}", "WARNING")

                # Show what loss/win was prevented
                original_result = signal.get('result', 'unknown')
                if original_result == 'win':
                    print_colored(f"   📉 Win missed", "WARNING")
                elif original_result == 'loss':
                    print_colored(f"   📈 Loss prevented!", "SUCCESS")
                else:
                    print_colored(f"   ⏳ Pending/Unknown result", "INFO")
                
            else:
                executed_signals += 1

                # Test the strategy
                strategy_signal, confidence = self.strategy_engine.evaluate_strategy_5(df)

                execution_details.append({
                    'signal_num': i,
                    'time': signal['timestamp'],
                    'pair': signal['pair'],
                    'direction': signal['direction'],
                    'filter_score': filter_result['score'],
                    'strategy_signal': strategy_signal,
                    'confidence': confidence,
                    'original_result': signal.get('result', 'unknown')
                })

                if strategy_signal != 0:
                    print_colored(f"✅ EXECUTED: Signal {strategy_signal}, Confidence: {confidence:.2f}", "SUCCESS")

                    # Use original result for simulation
                    original_result = signal.get('result', 'unknown')
                    if original_result == 'win':
                        simulated_wins += 1
                        print_colored(f"   📈 Result: WIN", "SUCCESS")
                    elif original_result == 'loss':
                        simulated_losses += 1
                        print_colored(f"   📉 Result: LOSS", "ERROR")
                    else:
                        print_colored(f"   ⏳ Result: PENDING/UNKNOWN", "WARNING")
                else:
                    print_colored(f"❌ NO SIGNAL: Strategy returned no signal", "WARNING")
        
        # Summary Results
        self.print_test_summary(s5_signals, filtered_signals, executed_signals, 
                               simulated_wins, simulated_losses, filter_details, execution_details)
    
    def print_test_summary(self, original_signals, filtered_signals, executed_signals, 
                          simulated_wins, simulated_losses, filter_details, execution_details):
        """Print comprehensive test summary"""
        
        print_colored("\n" + "="*80, "HEADER")
        print_colored("📊 COMPREHENSIVE TEST RESULTS", "HEADER", bold=True)
        print_colored("="*80, "HEADER")
        
        # Original Performance
        original_wins = len([s for s in original_signals if s.get('result') == 'win'])
        original_losses = len([s for s in original_signals if s.get('result') == 'loss'])
        original_pending = len([s for s in original_signals if s.get('result') == 'pending'])
        
        print_colored("\n🔍 ORIGINAL STRATEGY 5 PERFORMANCE (August 8th)", "HEADER", bold=True)
        print_colored(f"Total Signals: {len(original_signals)}", "INFO")
        print_colored(f"Wins: {original_wins}", "SUCCESS")
        print_colored(f"Losses: {original_losses}", "ERROR")
        print_colored(f"Pending: {original_pending}", "WARNING")
        
        if original_wins + original_losses > 0:
            original_win_rate = (original_wins / (original_wins + original_losses)) * 100
            print_colored(f"Win Rate: {original_win_rate:.1f}%", "SUCCESS" if original_win_rate >= 60 else "ERROR")
        
        # New Performance with Filter
        print_colored("\n🛡️ NEW STRATEGY 5 PERFORMANCE (With Filter)", "HEADER", bold=True)
        print_colored(f"Signals Filtered Out: {filtered_signals}", "WARNING")
        print_colored(f"Signals Executed: {executed_signals}", "SUCCESS")
        print_colored(f"Simulated Wins: {simulated_wins}", "SUCCESS")
        print_colored(f"Simulated Losses: {simulated_losses}", "ERROR")
        
        if simulated_wins + simulated_losses > 0:
            new_win_rate = (simulated_wins / (simulated_wins + simulated_losses)) * 100
            print_colored(f"New Win Rate: {new_win_rate:.1f}%", "SUCCESS" if new_win_rate >= 60 else "WARNING")
        else:
            print_colored("New Win Rate: N/A (No signals executed)", "INFO")
        
        # Filter Impact Analysis
        print_colored("\n💡 FILTER IMPACT ANALYSIS", "HEADER", bold=True)
        
        filtered_wins = len([f for f in filter_details if f['original_result'] == 'win'])
        filtered_losses = len([f for f in filter_details if f['original_result'] == 'loss'])
        
        print_colored(f"Wins Missed (due to filtering): {filtered_wins}", "WARNING")
        print_colored(f"Losses Prevented (due to filtering): {filtered_losses}", "SUCCESS")
        
        net_improvement = filtered_losses - filtered_wins
        if net_improvement > 0:
            print_colored(f"Net Improvement: +{net_improvement} trades", "SUCCESS")
        elif net_improvement < 0:
            print_colored(f"Net Impact: {net_improvement} trades", "WARNING")
        else:
            print_colored("Net Impact: Neutral", "INFO")
        
        # Capital Preservation
        print_colored(f"\n💰 CAPITAL PRESERVATION", "HEADER", bold=True)
        print_colored(f"Trades Avoided: {filtered_signals}", "INFO")
        print_colored(f"Capital Preserved: {filtered_signals} × trade_amount", "SUCCESS")
        print_colored("Capital available for better opportunities in trending markets", "SUCCESS")
        
        # Recommendations
        print_colored(f"\n🎯 RECOMMENDATIONS", "HEADER", bold=True)
        
        if filtered_signals == len(original_signals):
            print_colored("✅ All sideways market signals successfully filtered", "SUCCESS")
            print_colored("✅ Strategy 5 will only execute in trending markets", "SUCCESS")
            print_colored("✅ Capital preserved for optimal trading conditions", "SUCCESS")
        
        print_colored("🔧 To adjust filter sensitivity:", "INFO")
        print_colored("   • Lower threshold (0.5): More signals, higher risk", "WARNING")
        print_colored("   • Higher threshold (0.7+): Fewer signals, lower risk", "SUCCESS")
        print_colored("   • Current threshold (0.6): Balanced approach", "INFO")

def main():
    """Main test function"""
    print_colored("🚀 COMPREHENSIVE STRATEGY 5 FILTER TEST", "HEADER", bold=True)
    print_colored("Testing updated Strategy 5 with August 8th signals data", "INFO")
    print_colored("="*80, "HEADER")
    
    # Initialize test suite
    test_suite = Strategy5ComprehensiveTest()
    
    # Load August 8th signals
    signals_file = "signals/signals_2025-08-08.json"
    
    if not os.path.exists(signals_file):
        print_colored(f"❌ Signals file not found: {signals_file}", "ERROR")
        print_colored("Please ensure the signals file exists", "WARNING")
        return
    
    signals_data = test_suite.load_signals_data(signals_file)
    if not signals_data:
        return
    
    # Run comprehensive test
    test_suite.test_strategy_5_with_filter(signals_data)
    
    print_colored("\n✅ COMPREHENSIVE TEST COMPLETED!", "SUCCESS", bold=True)
    print_colored("The updated Strategy 5 with market condition filter is ready for use.", "SUCCESS")

if __name__ == "__main__":
    main()
