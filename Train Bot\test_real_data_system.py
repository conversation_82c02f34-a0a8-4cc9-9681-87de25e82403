#!/usr/bin/env python3
"""
Test script to verify the real data fetching system works correctly
Tests the enhanced real data fetcher with browser-based Quotex integration
"""

import sys
import os
import asyncio
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from quotex_integration import get_quotex_client
    from real_data_fetcher import fetch_guaranteed_real_data
    from data_validator import validate_market_data, log_validation_result
    from utils import print_colored
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Quotex credentials
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"

# Test assets that were previously failing
PROBLEMATIC_ASSETS = [
    "USDBDT_otc",
    "USDARS_otc", 
    "USDEGP_otc",
    "USDINR_otc",
    "USDMXN_otc",
    "USDNGN_otc",
    "USDPKR_otc"
]

# Test assets that should work
WORKING_ASSETS = [
    "EURUSD_otc",
    "GBPUSD_otc",
    "USDJPY_otc",
    "XAUUSD_otc"
]

async def test_connection():
    """Test browser-based Quotex connection"""
    print_colored("\n🧪 Testing Browser-Based Quotex Connection...", "INFO", bold=True)
    
    try:
        # Create browser-based client
        print_colored("🔗 Creating browser-based Quotex client...", "INFO")
        client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=True)
        
        # Connect
        print_colored("🔐 Connecting to Quotex...", "INFO")
        connected = await client.connect()
        
        if connected:
            print_colored("✅ Browser-based connection successful!", "SUCCESS")
            
            # Check connection status
            if hasattr(client, 'check_connect'):
                print_colored(f"🔗 Connection status: {client.check_connect}", "INFO")
            
            # Get balance to verify connection
            try:
                balance = await client.get_balance()
                print_colored(f"💰 Account balance: ${balance:.2f}", "SUCCESS")
            except Exception as e:
                print_colored(f"⚠️ Balance check failed: {e}", "WARNING")
            
            return client
        else:
            print_colored("❌ Connection failed", "ERROR")
            return None
            
    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")
        return None

async def test_data_fetching(client, asset):
    """Test real data fetching for a specific asset"""
    print_colored(f"\n📊 Testing {asset}...", "INFO")
    
    try:
        # Fetch data using the real data fetcher
        df = await fetch_guaranteed_real_data(client, asset, "M1", 50)
        
        if df is not None and len(df) > 0:
            print_colored(f"✅ {asset}: Data fetched ({len(df)} candles)", "SUCCESS")
            
            # Validate data quality
            validation_result = validate_market_data(df, asset)
            
            if validation_result['is_valid']:
                print_colored(f"✅ {asset}: Data validation PASSED", "SUCCESS")
                
                # Show sample data
                latest = df.iloc[-1]
                print_colored(f"   Latest: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}", "INFO")
                
                return True
            else:
                print_colored(f"❌ {asset}: Data validation FAILED", "ERROR")
                log_validation_result(validation_result)
                return False
        else:
            print_colored(f"❌ {asset}: No data received", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ {asset}: Error - {e}", "ERROR")
        return False

async def main():
    """Run comprehensive real data system test"""
    print_colored("🚀 REAL DATA FETCHING SYSTEM TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 Testing enhanced real data fetcher with validation", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Test connection
    client = await test_connection()
    
    if not client:
        print_colored("\n❌ CONNECTION FAILED - Cannot proceed with data tests", "ERROR", bold=True)
        return
    
    print_colored("\n🧪 Testing Previously Problematic Assets...", "WARNING", bold=True)
    
    problematic_results = {}
    for asset in PROBLEMATIC_ASSETS:
        try:
            success = await test_data_fetching(client, asset)
            problematic_results[asset] = success
        except Exception as e:
            print_colored(f"❌ Test error for {asset}: {e}", "ERROR")
            problematic_results[asset] = False
    
    print_colored("\n🧪 Testing Known Working Assets...", "INFO", bold=True)
    
    working_results = {}
    for asset in WORKING_ASSETS:
        try:
            success = await test_data_fetching(client, asset)
            working_results[asset] = success
        except Exception as e:
            print_colored(f"❌ Test error for {asset}: {e}", "ERROR")
            working_results[asset] = False
    
    # Summary
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📊 TEST RESULTS SUMMARY", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Problematic assets results
    problematic_success = sum(1 for success in problematic_results.values() if success)
    problematic_total = len(problematic_results)
    
    print_colored(f"\n🔍 Previously Problematic Assets: {problematic_success}/{problematic_total} now working", 
                 "SUCCESS" if problematic_success > 0 else "ERROR")
    
    for asset, success in problematic_results.items():
        status = "✅ NOW WORKING" if success else "❌ Still failing"
        color = "SUCCESS" if success else "ERROR"
        print_colored(f"   {asset}: {status}", color)
    
    # Working assets results
    working_success = sum(1 for success in working_results.values() if success)
    working_total = len(working_results)
    
    print_colored(f"\n✅ Known Working Assets: {working_success}/{working_total} working", 
                 "SUCCESS" if working_success == working_total else "WARNING")
    
    for asset, success in working_results.items():
        status = "✅ WORKING" if success else "❌ Failed"
        color = "SUCCESS" if success else "ERROR"
        print_colored(f"   {asset}: {status}", color)
    
    # Overall verdict
    total_success = problematic_success + working_success
    total_tested = problematic_total + working_total
    
    print_colored(f"\n📈 OVERALL RESULTS: {total_success}/{total_tested} assets working", "INFO", bold=True)
    
    if problematic_success >= problematic_total * 0.5:  # 50% of problematic assets now working
        print_colored("🎉 SUCCESS: Previously problematic assets are now working!", "SUCCESS", bold=True)
        print_colored("✅ Real data fetching system is functional!", "SUCCESS", bold=True)
        print_colored("✅ No more fake signals - only real market data!", "SUCCESS", bold=True)
    elif total_success >= total_tested * 0.7:  # 70% overall success
        print_colored("✅ GOOD: Most assets are working with real data!", "SUCCESS", bold=True)
        print_colored("⚠️ Some problematic assets may need additional work", "WARNING")
    else:
        print_colored("⚠️ PARTIAL: Some assets working, but more fixes needed", "WARNING", bold=True)
        print_colored("❌ Continue working on data fetching improvements", "ERROR")
    
    print_colored("=" * 80, "SKY_BLUE")

if __name__ == "__main__":
    asyncio.run(main())
