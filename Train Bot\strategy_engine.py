#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG, STRATEGY_5_FILTER_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG

        # Strategy 5 market condition filter settings from config
        self._debug_mode = STRATEGY_5_FILTER_CONFIG.get("DEBUG_MODE", False)
        self.strategy_5_filter_enabled = STRATEGY_5_FILTER_CONFIG.get("ENABLED", True)
        self.strategy_5_min_trend_strength = STRATEGY_5_FILTER_CONFIG.get("MIN_TREND_STRENGTH", 0.6)

    def set_debug_mode(self, enabled=True):
        """Enable or disable debug mode for strategy filtering feedback"""
        self._debug_mode = enabled
        if enabled:
            print_colored("🔧 Strategy Engine debug mode enabled", "INFO")

    def set_strategy_5_trend_threshold(self, threshold):
        """Set the minimum trend strength threshold for Strategy 5 execution"""
        if 0.0 <= threshold <= 1.0:
            self.strategy_5_min_trend_strength = threshold
            print_colored(f"⚙️ Strategy 5 trend threshold set to {threshold:.2f}", "INFO")
        else:
            print_colored("❌ Trend threshold must be between 0.0 and 1.0", "ERROR")

    def calculate_dynamic_periods(self, user_candles):
        """Calculate dynamic periods based on user-provided candle count"""
        return {
            'rsi_period': max(7, min(21, user_candles // 3)),  # 7-21 range, scales with user candles
            'recent_data_period': max(5, user_candles // 2),   # Half of user candles for recent analysis
            'extended_data_period': max(10, int(user_candles * 0.8)),  # 80% of user candles for S/R
            'divergence_lookback': max(2, user_candles // 12), # Proportional divergence lookback
            'ema_short': max(8, user_candles // 4),            # Short EMA period
            'ema_long': max(15, user_candles // 2),            # Long EMA period
            'volume_period': max(5, user_candles // 5),        # Volume analysis period
            'trend_period': max(10, user_candles // 3)         # Trend analysis period
        }

    def analyze_market_trend(self, full_df):
        """Analyze overall market trend using full dataset"""
        try:
            if len(full_df) < 20:
                return "sideways", 0.5

            # Calculate longer-term EMAs for trend analysis
            ema_short = full_df['close'].ewm(span=20).mean()
            ema_long = full_df['close'].ewm(span=50).mean()

            current_short = ema_short.iloc[-1]
            current_long = ema_long.iloc[-1]

            # Calculate trend strength
            price_change = (full_df['close'].iloc[-1] - full_df['close'].iloc[-20]) / full_df['close'].iloc[-20]

            # Determine trend direction and strength
            if current_short > current_long and price_change > 0.002:  # 0.2% threshold
                trend_strength = min(0.9, abs(price_change) * 100)
                return "bullish", trend_strength
            elif current_short < current_long and price_change < -0.002:
                trend_strength = min(0.9, abs(price_change) * 100)
                return "bearish", trend_strength
            else:
                return "sideways", 0.5

        except Exception:
            return "sideways", 0.5

    def evaluate_strategy_1(self, df, user_candles=None):
        """Strategy 1: Enhanced Breakout with Volume (OPTIMIZED FOR SIGNALS + ACCURACY)"""
        if user_candles is None:
            user_candles = len(df)

        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            # Get current candle data
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current

            # Calculate dynamic periods based on user candles
            periods = self.calculate_dynamic_periods(user_candles)
            window = periods['trend_period']

            # Calculate dynamic support/resistance using user-specified data
            recent_highs = df['high'].tail(window)
            recent_lows = df['low'].tail(window)
            resistance = recent_highs.quantile(0.85)  # 85th percentile instead of max
            support = recent_lows.quantile(0.15)     # 15th percentile instead of min

            # Current candle values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']

            # Volume analysis using dynamic period
            volume_period = periods['volume_period']
            avg_volume = df['volume'].tail(volume_period).mean()
            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            total_range = high - low

            # Price momentum check
            price_momentum = (close - prev['close']) / prev['close'] if prev['close'] > 0 else 0

            # ENHANCED BUY signal conditions (BALANCED for signals + accuracy)
            buy_conditions = [
                close > resistance * 0.9995,  # Near or above resistance (slightly relaxed)
                close > open_,                 # Bullish candle
                volume_ratio > 1.2,           # Above average volume (relaxed from 1.5)
                upper_wick < body * 0.5,      # Not too much upper wick (relaxed)
                price_momentum > 0.0001,      # Positive momentum
                body > total_range * 0.4      # Strong body (at least 40% of range)
            ]

            if sum(buy_conditions) >= 5:  # Need at least 5 out of 6 conditions
                confidence = 0.75 + (sum(buy_conditions) - 5) * 0.05  # 75-80% confidence
                return 1, min(confidence, 0.85)

            # ENHANCED SELL signal conditions (BALANCED for signals + accuracy)
            sell_conditions = [
                close < support * 1.0005,     # Near or below support (slightly relaxed)
                close < open_,                # Bearish candle
                volume_ratio > 1.2,          # Above average volume (relaxed)
                lower_wick < body * 0.5,     # Not too much lower wick (relaxed)
                price_momentum < -0.0001,    # Negative momentum
                body > total_range * 0.4     # Strong body (at least 40% of range)
            ]

            if sum(sell_conditions) >= 5:  # Need at least 5 out of 6 conditions
                confidence = 0.75 + (sum(sell_conditions) - 5) * 0.05  # 75-80% confidence
                return -1, min(confidence, 0.85)

            return 0, 0.0  # No signal

        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df, user_candles=None):
        """Strategy 2: PULLBACK ENTRY (High Accuracy Version)"""
        if user_candles is None:
            user_candles = len(df)

        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            # Calculate technical indicators
            df_copy = df.copy()

            # Calculate dynamic periods based on user candles
            periods = self.calculate_dynamic_periods(user_candles)

            # Calculate EMA using dynamic period
            ema_period = periods['ema_short']
            df_copy['ema_20'] = df_copy['close'].ewm(span=ema_period).mean()

            # Calculate RSI using dynamic period
            rsi_period = periods['rsi_period']
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            current = df_copy.iloc[-1]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Candle structure analysis
            body = abs(close - open_)
            total_range = high - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Volume confirmation using dynamic period
            volume_period = periods['volume_period']
            vol_avg = df_copy['volume'].tail(volume_period).mean()
            volume_confirmed = volume > vol_avg * 1.2

            # Pullback detection - near EMA20
            pullback_tolerance = 0.008  # 0.8%
            near_ema = abs(close - ema_20) / ema_20 < pullback_tolerance if ema_20 > 0 else False

            # Trend strength analysis
            recent_closes = df_copy['close'].tail(6)
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # EMA trend confirmation
            ema_values = df_copy['ema_20'].tail(5)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4



            # Moderate candle requirements - less restrictive
            good_bullish = (close > open_ and body_ratio > 0.3)
            good_bearish = (close < open_ and body_ratio > 0.3)

            # BUY: Pullback entry in uptrend - relaxed conditions
            if (good_bullish and                # Good bullish candle
                near_ema and                    # Pullback to EMA
                uptrend_strength >= 3 and       # Moderate uptrend (3+ up candles)
                ema_rising and                  # EMA rising
                volume_confirmed and            # Volume confirmation
                40 <= rsi <= 80):               # Wider RSI range
                return 1, 0.88

            # SELL: Pullback entry in downtrend - relaxed conditions
            elif (good_bearish and             # Good bearish candle
                  near_ema and                 # Pullback to EMA
                  downtrend_strength >= 3 and  # Moderate downtrend (3+ down candles)
                  ema_falling and              # EMA falling
                  volume_confirmed and         # Volume confirmation
                  20 <= rsi <= 60):            # Wider RSI range
                return -1, 0.88

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df, user_candles=None):
        """Evaluate Strategy 3: Support/Resistance Rejection"""
        if user_candles is None:
            user_candles = len(df)

        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current

            # Calculate dynamic periods based on user candles
            periods = self.calculate_dynamic_periods(user_candles)
            lookback = periods['extended_data_period']
            wick_ratio_threshold = 0.4
            
            # Calculate body and wicks
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Find support/resistance levels
            resistance_levels = df['high'].tail(lookback).nlargest(3).values
            support_levels = df['low'].tail(lookback).nsmallest(3).values
            
            # Check resistance rejection (SELL signal)
            for resistance in resistance_levels:
                if (abs(high - resistance) <= 0.0001 and  # Touch resistance
                    close < open_ and  # Red candle
                    upper_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return -1, 0.75
                    
            # Check support bounce (BUY signal)
            for support in support_levels:
                if (abs(low - support) <= 0.0001 and  # Touch support
                    close > open_ and  # Green candle
                    lower_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return 1, 0.75
                    
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df, user_candles=None):
        """Evaluate Strategy 4: Trendline Break with Rejection"""
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 4 parameters
            lookback = 50
            min_wick_ratio = 0.3
            
            # Get current values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Simplified trendline detection
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            
            # Check for uptrend trendline rejection
            if (close > open_ and  # Green candle
                upper_wick >= min_wick_ratio * body and
                volume <= prev_volume and
                recent_lows.iloc[-1] > recent_lows.iloc[-10]):  # Simple uptrend check
                return -1, 0.7
                
            # Check for downtrend trendline rejection  
            elif (close < open_ and  # Red candle
                  lower_wick >= min_wick_ratio * body and
                  volume <= prev_volume and
                  recent_highs.iloc[-1] < recent_highs.iloc[-10]):  # Simple downtrend check
                return 1, 0.7
                
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_5(self, df, user_candles=None):
        """Strategy 5: OPTIMIZED Pre-Candle Momentum Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Trending Markets - RELAXED CONDITIONS for more signals
        Indicators: EMA 5 & EMA 9, RSI(14), Volume Analysis

        MARKET CONDITION FILTER: Only executes in trending markets (trend_strength >= 0.6)
        """
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            # MARKET CONDITION FILTER - Strategy 5 is designed for trending markets only
            if self.strategy_5_filter_enabled:
                market_trend, trend_strength = self.analyze_market_trend(df)

                if trend_strength < self.strategy_5_min_trend_strength:
                    # Market is sideways/weak trend - Strategy 5 should not execute
                    if self._debug_mode:
                        print_colored(f"🚫 Strategy 5 filtered out: Market trend '{market_trend}' with strength {trend_strength:.2f} < {self.strategy_5_min_trend_strength:.2f}", "WARNING")
                    return 0, 0.0

                # Market is trending - proceed with Strategy 5 logic
                if self._debug_mode:
                    print_colored(f"✅ Strategy 5 executing: Market trend '{market_trend}' with strength {trend_strength:.2f} >= {self.strategy_5_min_trend_strength:.2f}", "SUCCESS")

            # Calculate indicators using dynamic periods
            df_copy = df.copy()

            # Calculate dynamic periods based on user candles
            periods = self.calculate_dynamic_periods(user_candles)

            # EMA using dynamic periods
            ema_short = periods['ema_short']
            ema_long = periods['ema_long']
            df_copy['ema_short'] = df_copy['close'].ewm(span=ema_short).mean()
            df_copy['ema_long'] = df_copy['close'].ewm(span=ema_long).mean()

            # RSI using dynamic period
            rsi_period = periods['rsi_period']
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=10).mean()  # Reduced window
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()  # Reduced window

            # Get current values
            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED trend check - allow weaker trends
            adx_threshold = 20  # Reduced from 25
            if current['adx'] < adx_threshold:
                return 0, 0.0

            # Check trend direction using dynamic EMAs
            uptrend = current['ema_short'] > current['ema_long']
            downtrend = current['ema_short'] < current['ema_long']

            # Volume check using dynamic period
            volume_period = periods['volume_period']
            avg_volume = df_copy['volume'].tail(volume_period).mean()
            volume_ok = current['volume'] > (avg_volume * 1.1)

            # RSI momentum check
            rsi_rising = current['rsi'] > prev['rsi']
            rsi_falling = current['rsi'] < prev['rsi']

            # Price position check using dynamic EMA
            price_near_ema = abs(current['close'] - current['ema_short']) / current['close'] < 0.003

            # EMA separation check (trend strength)
            ema_separation = abs(current['ema_short'] - current['ema_long']) / current['close']
            strong_trend = ema_separation > 0.0005

            # OPTIMIZED CALL Signal Logic (MORE SIGNALS)
            call_conditions = [
                uptrend,
                current['rsi'] > 40 and current['rsi'] < 75,  # Wider RSI range
                rsi_rising or current['rsi'] > 55,             # RSI rising OR above 55
                volume_ok,
                price_near_ema or strong_trend                # Price near EMA OR strong trend
            ]

            if sum(call_conditions) >= 4:  # Need 4 out of 5 conditions
                confidence = 0.72 + (sum(call_conditions) - 4) * 0.06  # 72-78% confidence
                return 1, min(confidence, 0.82)

            # OPTIMIZED PUT Signal Logic (MORE SIGNALS)
            put_conditions = [
                downtrend,
                current['rsi'] > 25 and current['rsi'] < 60,   # Wider RSI range
                rsi_falling or current['rsi'] < 45,            # RSI falling OR below 45
                volume_ok,
                price_near_ema or strong_trend                # Price near EMA OR strong trend
            ]

            if sum(put_conditions) >= 4:  # Need 4 out of 5 conditions
                confidence = 0.72 + (sum(put_conditions) - 4) * 0.06  # 72-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 5: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_6(self, df, user_candles=None):
        """Strategy 6: OPTIMIZED Reversal Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Ranging Markets - RELAXED CONDITIONS for more signals
        Indicators: Bollinger Bands, Stochastic, Support/Resistance
        """
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Bollinger Bands (15, 1.8) - More responsive
            df_copy['bb_middle'] = df_copy['close'].rolling(window=15).mean()  # Reduced window
            bb_std = df_copy['close'].rolling(window=15).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 1.8)  # Reduced multiplier
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 1.8)

            # Stochastic (8,3,3) - More responsive
            low_min = df_copy['low'].rolling(window=8).min()
            high_max = df_copy['high'].rolling(window=8).max()
            k_percent = 100 * ((df_copy['close'] - low_min) / (high_max - low_min))
            df_copy['stoch_k'] = k_percent.rolling(window=3).mean()
            df_copy['stoch_d'] = df_copy['stoch_k'].rolling(window=3).mean()

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=10).mean()  # Reduced window
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()

            # Dynamic Support and Resistance levels
            recent_data = df_copy.tail(15)  # Shorter period
            resistance = recent_data['high'].quantile(0.9)  # 90th percentile
            support = recent_data['low'].quantile(0.1)      # 10th percentile

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED ranging market check
            if current['adx'] >= 30:  # Increased from 25 to allow more signals
                return 0, 0.0

            # Remove squeeze requirement - allow all BB widths

            # Candle analysis
            body = abs(current['close'] - current['open'])
            upper_wick = current['high'] - max(current['close'], current['open'])
            lower_wick = min(current['close'], current['open']) - current['low']
            total_range = current['high'] - current['low']

            # RELAXED candlestick patterns
            bearish_engulfing = (prev['close'] > prev['open'] and current['close'] < current['open'] and
                               current['open'] > prev['close'] and current['close'] < prev['open'])
            pin_bar_top = upper_wick > (body * 1.5) and lower_wick < (body * 0.7)  # Relaxed
            doji_top = body < total_range * 0.2 and upper_wick > total_range * 0.4

            bullish_engulfing = (prev['close'] < prev['open'] and current['close'] > current['open'] and
                               current['open'] < prev['close'] and current['close'] > prev['open'])
            hammer = lower_wick > (body * 1.5) and upper_wick < (body * 0.7)  # Relaxed
            doji_bottom = body < total_range * 0.2 and lower_wick > total_range * 0.4

            # Volume confirmation
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > avg_volume * 0.8  # Relaxed volume requirement

            # OPTIMIZED PUT Signal (MORE SIGNALS)
            put_conditions = [
                current['close'] >= current['bb_upper'] * 0.95,  # Near upper band (relaxed)
                current['stoch_k'] > 70,                         # Overbought (relaxed from 80)
                bearish_engulfing or pin_bar_top or doji_top,    # Any bearish pattern
                current['close'] >= resistance * 0.995,         # Near resistance (relaxed)
                volume_ok                                        # Volume confirmation
            ]

            if sum(put_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.70 + (sum(put_conditions) - 3) * 0.05  # 70-80% confidence
                return -1, min(confidence, 0.85)

            # OPTIMIZED CALL Signal (MORE SIGNALS)
            call_conditions = [
                current['close'] <= current['bb_lower'] * 1.05,  # Near lower band (relaxed)
                current['stoch_k'] < 30,                         # Oversold (relaxed from 20)
                bullish_engulfing or hammer or doji_bottom,      # Any bullish pattern
                current['close'] <= support * 1.005,            # Near support (relaxed)
                volume_ok                                        # Volume confirmation
            ]

            if sum(call_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.70 + (sum(call_conditions) - 3) * 0.05  # 70-80% confidence
                return 1, min(confidence, 0.85)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 6: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_7(self, df, user_candles=None):
        """Strategy 7: OPTIMIZED Breakout Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Breakout Opportunities - RELAXED CONDITIONS for more signals
        Indicators: Key Levels, Volume, MACD
        """
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # MACD (8, 17, 6) - More responsive
            exp1 = df_copy['close'].ewm(span=8).mean()
            exp2 = df_copy['close'].ewm(span=17).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=6).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Bollinger Bands for volatility check
            df_copy['bb_middle'] = df_copy['close'].rolling(window=15).mean()  # Shorter period
            bb_std = df_copy['close'].rolling(window=15).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 1.8)  # Reduced multiplier
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 1.8)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # RELAXED volatility check
            bb_width = (current['bb_upper'] - current['bb_lower']) / current['bb_middle']
            if bb_width < 0.015:  # Reduced threshold
                return 0, 0.0

            # Key levels from recent data
            recent_data = df_copy.tail(12)  # Shorter lookback
            key_high = recent_data['high'].max()
            key_low = recent_data['low'].min()

            # Secondary levels
            second_high = recent_data['high'].nlargest(2).iloc[1] if len(recent_data) > 1 else key_high
            second_low = recent_data['low'].nsmallest(2).iloc[1] if len(recent_data) > 1 else key_low

            # RELAXED volume check
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > (avg_volume * 1.3)  # Reduced from 2.0

            # MACD momentum check (not requiring cross)
            macd_bullish = current['macd'] > current['macd_signal']
            macd_bearish = current['macd'] < current['macd_signal']
            macd_strengthening = abs(current['macd_histogram']) > abs(prev['macd_histogram'])

            # Price position analysis
            near_key_high = abs(current['close'] - key_high) / current['close'] < 0.002  # Relaxed
            near_key_low = abs(current['close'] - key_low) / current['close'] < 0.002   # Relaxed
            near_second_high = abs(current['close'] - second_high) / current['close'] < 0.002
            near_second_low = abs(current['close'] - second_low) / current['close'] < 0.002

            # Price momentum
            price_momentum = (current['close'] - prev['close']) / prev['close']

            # OPTIMIZED CALL Signal (MORE SIGNALS)
            call_conditions = [
                near_key_high or near_second_high,           # Near resistance level
                current['close'] > key_high * 0.998,        # Above or near key high (relaxed)
                macd_bullish or macd_strengthening,         # MACD bullish OR strengthening
                volume_ok,                                   # Volume confirmation
                price_momentum > 0,                         # Positive momentum
                current['close'] > current['bb_middle']     # Above middle BB
            ]

            if sum(call_conditions) >= 4:  # Need 4 out of 6 conditions
                confidence = 0.70 + (sum(call_conditions) - 4) * 0.04  # 70-78% confidence
                return 1, min(confidence, 0.82)

            # OPTIMIZED PUT Signal (MORE SIGNALS)
            put_conditions = [
                near_key_low or near_second_low,            # Near support level
                current['close'] < key_low * 1.002,        # Below or near key low (relaxed)
                macd_bearish or macd_strengthening,        # MACD bearish OR strengthening
                volume_ok,                                  # Volume confirmation
                price_momentum < 0,                        # Negative momentum
                current['close'] < current['bb_middle']    # Below middle BB
            ]

            if sum(put_conditions) >= 4:  # Need 4 out of 6 conditions
                confidence = 0.70 + (sum(put_conditions) - 4) * 0.04  # 70-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 7: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_8(self, df, user_candles=None):
        """Strategy 8: Pre-Candle Fakeout Strategy (85%+ Accuracy in Choppy Markets)
        Best for: False Breakout Traps
        Indicators: Fibonacci Retracement (38.2%, 61.8%), RSI Divergence
        """
        if user_candles is None:
            user_candles = len(df)

        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate dynamic periods based on user candles
            periods = self.calculate_dynamic_periods(user_candles)

            # RSI for divergence detection using dynamic period
            rsi_period = periods['rsi_period']
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Find recent swing high and low for Fibonacci levels (dynamic period)
            recent_period = periods['recent_data_period']
            recent_data = df_copy.tail(recent_period)
            swing_high = recent_data['high'].max()
            swing_low = recent_data['low'].min()

            # Calculate Fibonacci retracement levels
            fib_range = swing_high - swing_low
            fib_382 = swing_high - (fib_range * 0.382)
            fib_618 = swing_high - (fib_range * 0.618)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]
            prev2 = df_copy.iloc[-3] if len(df_copy) > 2 else df_copy.iloc[-2]

            # Support and resistance levels (dynamic period)
            extended_period = periods['extended_data_period']
            extended_data = df_copy.tail(extended_period)
            resistance_level = extended_data['high'].max()
            support_level = extended_data['low'].min()

            # RSI divergence detection
            # Bullish divergence: Price makes lower low, RSI makes higher low
            price_lower_low = current['low'] < prev2['low'] and prev['low'] < prev2['low']
            rsi_higher_low = current['rsi'] > prev2['rsi'] and prev['rsi'] > prev2['rsi']
            bullish_divergence = price_lower_low and rsi_higher_low

            # Bearish divergence: Price makes higher high, RSI makes lower high
            price_higher_high = current['high'] > prev2['high'] and prev['high'] > prev2['high']
            rsi_lower_high = current['rsi'] < prev2['rsi'] and prev['rsi'] < prev2['rsi']
            bearish_divergence = price_higher_high and rsi_lower_high

            # Fakeout detection: Brief break of level but quick reversal
            # Check if price briefly broke a level in previous candles but is now reversing

            # Fake breakdown detection (for CALL signals)
            fake_breakdown = (prev['low'] < support_level * 0.999 and  # Previous candle broke support
                             current['close'] > support_level and  # Current candle back above support
                             bullish_divergence)  # RSI shows bullish divergence

            # Fake breakout detection (for PUT signals)
            fake_breakout = (prev['high'] > resistance_level * 1.001 and  # Previous candle broke resistance
                            current['close'] < resistance_level and  # Current candle back below resistance
                            bearish_divergence)  # RSI shows bearish divergence

            # Check if price is near Fibonacci levels for confirmation
            near_fib_382 = abs(current['close'] - fib_382) / current['close'] < 0.001
            near_fib_618 = abs(current['close'] - fib_618) / current['close'] < 0.001

            # CALL Signal: Fake breakdown + bullish divergence + Fibonacci confirmation
            if fake_breakdown and (near_fib_382 or near_fib_618):
                confidence = 0.85
                if near_fib_618:  # Stronger level
                    confidence = 0.90
                return 1, confidence

            # PUT Signal: Fake breakout + bearish divergence + Fibonacci confirmation
            elif fake_breakout and (near_fib_382 or near_fib_618):
                confidence = 0.85
                if near_fib_618:  # Stronger level
                    confidence = 0.90
                return -1, confidence

            # Alternative fakeout detection without strict level breaks
            # Look for RSI divergence near key levels
            elif (bullish_divergence and current['close'] <= support_level * 1.002 and
                  (near_fib_382 or near_fib_618)):
                return 1, 0.80

            elif (bearish_divergence and current['close'] >= resistance_level * 0.998 and
                  (near_fib_382 or near_fib_618)):
                return -1, 0.80

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 8: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_9(self, df, user_candles=None):
        """Strategy 9: OPTIMIZED Ranging Market Strategy (BALANCED SIGNALS + ACCURACY)
        Best for: Sideways Markets - RELAXED CONDITIONS for more signals
        Indicators: Support/Resistance, RSI, EMA, Price Action
        """
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate RSI(14)
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Calculate EMA 20
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()

            # Simplified EMA slope calculation
            ema_values = df_copy['ema20'].tail(8).values  # Shorter period
            if len(ema_values) >= 2:
                x = np.arange(len(ema_values))
                slope = np.polyfit(x, ema_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema_values[-1] * 1000))
            else:
                ema_slope_degrees = 0

            # Simplified ADX calculation
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=10).mean()  # Shorter period
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=10).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=10).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=10).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # RELAXED market type detection
            market_type = 'unclear'
            if abs(ema_slope_degrees) < 5 and current['adx'] < 25:  # More lenient
                market_type = 'ranging'
            elif abs(ema_slope_degrees) >= 5 and current['adx'] > 25:
                market_type = 'trending'

            # Allow both ranging and unclear markets (more signals)
            if market_type == 'trending':
                return 0, 0.0

            # Dynamic support and resistance zones
            lookback = min(30, len(df_copy))  # Shorter lookback
            recent_data = df_copy.tail(lookback)

            # Multiple support/resistance levels
            support_levels = recent_data['low'].nsmallest(5).values  # More levels
            resistance_levels = recent_data['high'].nlargest(5).values

            # Current candle analysis
            open_price = current['open']
            high_price = current['high']
            low_price = current['low']
            close_price = current['close']

            body = abs(close_price - open_price)
            upper_wick = high_price - max(open_price, close_price)
            lower_wick = min(open_price, close_price) - low_price

            # RELAXED price action patterns
            bullish_pin_bar = (lower_wick > body * 1.5 and upper_wick < body * 0.7)  # Relaxed
            hammer = (lower_wick > body * 1.5 and upper_wick < body * 0.5)
            bullish_engulfing = (prev['close'] < prev['open'] and close_price > open_price and
                               open_price < prev['close'] and close_price > prev['open'])
            doji_bottom = body < (high_price - low_price) * 0.3 and lower_wick > upper_wick

            bearish_pin_bar = (upper_wick > body * 1.5 and lower_wick < body * 0.7)  # Relaxed
            shooting_star = (upper_wick > body * 1.5 and lower_wick < body * 0.5)
            bearish_engulfing = (prev['close'] > prev['open'] and close_price < open_price and
                               open_price > prev['close'] and close_price < prev['open'])
            doji_top = body < (high_price - low_price) * 0.3 and upper_wick > lower_wick

            # RELAXED proximity check
            near_support = False
            near_resistance = False

            for support in support_levels:
                if abs(close_price - support) / close_price < 0.004:  # Relaxed from 0.002
                    near_support = True
                    break

            for resistance in resistance_levels:
                if abs(close_price - resistance) / close_price < 0.004:  # Relaxed from 0.002
                    near_resistance = True
                    break

            # Volume confirmation
            avg_volume = df_copy['volume'].tail(8).mean()
            volume_ok = current['volume'] > avg_volume * 0.8  # Very relaxed

            # OPTIMIZED CALL Signal (MORE SIGNALS)
            call_conditions = [
                near_support,                                    # Near support level
                current['rsi'] < 40,                            # Oversold (relaxed from 30)
                bullish_pin_bar or hammer or bullish_engulfing or doji_bottom,  # Any bullish pattern
                abs(ema_slope_degrees) < 6,                     # Not strongly trending (relaxed)
                volume_ok                                       # Volume confirmation
            ]

            if sum(call_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.68 + (sum(call_conditions) - 3) * 0.05  # 68-78% confidence
                return 1, min(confidence, 0.82)

            # OPTIMIZED PUT Signal (MORE SIGNALS)
            put_conditions = [
                near_resistance,                                 # Near resistance level
                current['rsi'] > 60,                            # Overbought (relaxed from 70)
                bearish_pin_bar or shooting_star or bearish_engulfing or doji_top,  # Any bearish pattern
                abs(ema_slope_degrees) < 6,                     # Not strongly trending (relaxed)
                volume_ok                                       # Volume confirmation
            ]

            if sum(put_conditions) >= 3:  # Need 3 out of 5 conditions
                confidence = 0.68 + (sum(put_conditions) - 3) * 0.05  # 68-78% confidence
                return -1, min(confidence, 0.82)

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 9: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_10(self, df, user_candles=None):
        """Strategy 10: Trending Market Strategy (78-88% Accuracy)
        Best for: Trending Markets (EMA + MACD + Pullback Confirmation)
        Indicators: EMA 20/50, MACD(12,26,9), ADX, Support/Resistance
        """
        if user_candles is None:
            user_candles = len(df)
        if len(df) < 10 or user_candles < 10:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate EMAs
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()
            df_copy['ema50'] = df_copy['close'].ewm(span=50).mean()

            # Calculate MACD (12, 26, 9)
            exp1 = df_copy['close'].ewm(span=12).mean()
            exp2 = df_copy['close'].ewm(span=26).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=9).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Calculate EMA slope to detect trend
            ema20_values = df_copy['ema20'].tail(10).values
            if len(ema20_values) >= 2:
                x = np.arange(len(ema20_values))
                slope = np.polyfit(x, ema20_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema20_values[-1] * 1000))
            else:
                ema_slope_degrees = 0

            # ADX calculation for trend strength
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # STRICTER market type detection for HIGHER ACCURACY
            market_type = 'unclear'
            if abs(ema_slope_degrees) >= 3 and current['adx'] > 20:  # More strict
                market_type = 'trending'
            elif abs(ema_slope_degrees) < 2 and current['adx'] < 15:
                market_type = 'ranging'

            # Require clear trending conditions
            ema_separation = abs(current['ema20'] - current['ema50']) / current['close']
            strong_ema_trend = ema_separation > 0.002  # EMAs must be separated by 0.2%

            if market_type != 'trending' or not strong_ema_trend:
                return 0, 0.0

            # Find support and resistance levels
            lookback = min(50, len(df_copy))
            recent_data = df_copy.tail(lookback)
            support_levels = recent_data['low'].nsmallest(3).values
            resistance_levels = recent_data['high'].nlargest(3).values

            # Current price analysis
            close_price = current['close']
            open_price = current['open']

            # MACD cross detection
            macd_bullish_cross = (current['macd'] > current['macd_signal'] and
                                 prev['macd'] <= prev['macd_signal'])
            macd_bearish_cross = (current['macd'] < current['macd_signal'] and
                                 prev['macd'] >= prev['macd_signal'])

            # STRICTER price pullback to EMA20 detection for HIGHER ACCURACY
            near_ema20 = abs(close_price - current['ema20']) / close_price < 0.001  # Strict requirement

            # STRICTER candle pattern detection
            bullish_candle = close_price > open_price
            bearish_candle = close_price < open_price

            # Require meaningful candle body (no doji acceptance)
            body_size = abs(close_price - open_price) / close_price
            meaningful_body = body_size > 0.0005  # Must have meaningful body

            # STRICTER candle conditions
            good_bullish = bullish_candle and meaningful_body
            good_bearish = bearish_candle and meaningful_body

            # STRICTER proximity to support/resistance levels
            near_resistance = any(abs(close_price - resistance) / close_price < 0.002  # Strict
                                for resistance in resistance_levels)
            near_support = any(abs(close_price - support) / close_price < 0.002  # Strict
                             for support in support_levels)

            # Volume confirmation for higher accuracy
            avg_volume = df_copy['volume'].tail(10).mean()
            volume_confirmation = current['volume'] > avg_volume * 1.2

            # STRICT CALL (BUY) Signal - HIGH ACCURACY CONDITIONS
            if (current['ema20'] > current['ema50'] * 1.001 and  # Clear uptrend (0.1% separation)
                macd_bullish_cross and  # REQUIRE actual MACD cross (not just above)
                near_ema20 and  # MUST be near EMA20 (pullback entry)
                good_bullish and  # Strong bullish candle
                not near_resistance and  # No resistance nearby
                current['adx'] > 25 and  # Strong trend strength
                volume_confirmation):  # Volume confirmation
                confidence = 0.85
                if current['macd_histogram'] > prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.90
                return 1, confidence

            # STRICT PUT (SELL) Signal - HIGH ACCURACY CONDITIONS
            elif (current['ema20'] < current['ema50'] * 0.999 and  # Clear downtrend (0.1% separation)
                  macd_bearish_cross and  # REQUIRE actual MACD cross (not just below)
                  near_ema20 and  # MUST be near EMA20 (pullback entry)
                  good_bearish and  # Strong bearish candle
                  not near_support and  # No support nearby
                  current['adx'] > 25 and  # Strong trend strength
                  volume_confirmation):  # Volume confirmation
                confidence = 0.85
                if current['macd_histogram'] < prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.90
                return -1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 10: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df, selected_strategies=None, user_candles=None):
        """Evaluate all strategies and return combined signal"""
        if selected_strategies is None:
            selected_strategies = ['S1', 'S2', 'S3', 'S4', 'S5', 'S6', 'S7', 'S8', 'S9', 'S10']

        # CRITICAL: Verify data quality before strategy analysis
        if df is None or len(df) == 0:
            return 0, 0.0, "N/A"

        # Use actual data length if user_candles not provided
        if user_candles is None:
            user_candles = len(df)

        signals = {}

        # Evaluate each strategy if selected
        if 'S1' in selected_strategies:
            s1_signal, s1_conf = self.evaluate_strategy_1(df, user_candles)
            signals['S1'] = {'signal': s1_signal, 'confidence': s1_conf}

        if 'S2' in selected_strategies:
            s2_signal, s2_conf = self.evaluate_strategy_2(df, user_candles)
            signals['S2'] = {'signal': s2_signal, 'confidence': s2_conf}

        if 'S3' in selected_strategies:
            s3_signal, s3_conf = self.evaluate_strategy_3(df, user_candles)
            signals['S3'] = {'signal': s3_signal, 'confidence': s3_conf}

        if 'S4' in selected_strategies:
            s4_signal, s4_conf = self.evaluate_strategy_4(df, user_candles)
            signals['S4'] = {'signal': s4_signal, 'confidence': s4_conf}

        if 'S5' in selected_strategies:
            s5_signal, s5_conf = self.evaluate_strategy_5(df, user_candles)
            signals['S5'] = {'signal': s5_signal, 'confidence': s5_conf}

        if 'S6' in selected_strategies:
            s6_signal, s6_conf = self.evaluate_strategy_6(df, user_candles)
            signals['S6'] = {'signal': s6_signal, 'confidence': s6_conf}

        if 'S7' in selected_strategies:
            s7_signal, s7_conf = self.evaluate_strategy_7(df, user_candles)
            signals['S7'] = {'signal': s7_signal, 'confidence': s7_conf}

        if 'S8' in selected_strategies:
            s8_signal, s8_conf = self.evaluate_strategy_8(df, user_candles)
            signals['S8'] = {'signal': s8_signal, 'confidence': s8_conf}

        if 'S9' in selected_strategies:
            s9_signal, s9_conf = self.evaluate_strategy_9(df, user_candles)
            signals['S9'] = {'signal': s9_signal, 'confidence': s9_conf}

        if 'S10' in selected_strategies:
            s10_signal, s10_conf = self.evaluate_strategy_10(df, user_candles)
            signals['S10'] = {'signal': s10_signal, 'confidence': s10_conf}
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result in the expected format (signal, confidence, strategy)
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            return best_signal, best_confidence, best_strategy
        else:
            return 0, 0.0, selected_strategies[0] if selected_strategies else 'S1'

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))
