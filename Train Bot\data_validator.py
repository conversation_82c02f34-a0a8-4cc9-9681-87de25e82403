#!/usr/bin/env python3
"""
Data Validator Module for Trading Bot
Validates market data authenticity and quality to prevent fake signals
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
from utils import print_colored

class DataValidator:
    def __init__(self):
        """Initialize data validator"""
        self.min_price_variance = 0.00001  # Minimum price variance to consider data real
        self.min_candles_with_movement = 3  # Minimum candles that should show price movement
        self.max_static_candles_ratio = 0.8  # Maximum ratio of static candles allowed
    
    def validate_market_data(self, df: pd.DataFrame, asset: str) -> Dict[str, Any]:
        """
        Comprehensive validation of market data authenticity
        Returns validation result with detailed analysis
        """
        try:
            if df is None or len(df) == 0:
                return self._create_validation_result(False, asset, "No data provided")
            
            validation_checks = {
                'has_sufficient_data': self._check_sufficient_data(df),
                'has_price_movement': self._check_price_movement(df),
                'has_realistic_ohlc': self._check_realistic_ohlc(df),
                'has_volume_activity': self._check_volume_activity(df),
                'has_time_progression': self._check_time_progression(df),
                'price_variance_check': self._check_price_variance(df),
                'candle_movement_check': self._check_candle_movements(df)
            }
            
            # Calculate overall validity
            passed_checks = sum(1 for check in validation_checks.values() if check['passed'])
            total_checks = len(validation_checks)
            validity_score = passed_checks / total_checks
            
            # Data is considered valid if it passes at least 70% of checks
            is_valid = validity_score >= 0.7
            
            # Create detailed result
            result = self._create_validation_result(
                is_valid, asset, 
                f"Passed {passed_checks}/{total_checks} validation checks",
                validation_checks, validity_score
            )
            
            return result
            
        except Exception as e:
            print_colored(f"❌ Data validation error for {asset}: {e}", "ERROR")
            return self._create_validation_result(False, asset, f"Validation error: {str(e)}")
    
    def _check_sufficient_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if there's sufficient data for analysis"""
        sufficient = len(df) >= 10
        return {
            'passed': sufficient,
            'message': f"Data points: {len(df)}" + (" (sufficient)" if sufficient else " (insufficient)")
        }
    
    def _check_price_movement(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if prices show realistic movement over time"""
        try:
            if len(df) < 2:
                return {'passed': False, 'message': "Insufficient data for movement check"}
            
            # Check if prices change over time
            price_changes = df['close'].diff().abs()
            non_zero_changes = (price_changes > 0.000001).sum()
            movement_ratio = non_zero_changes / len(df)
            
            # At least 30% of candles should show some price movement
            has_movement = movement_ratio >= 0.3
            
            return {
                'passed': has_movement,
                'message': f"Price movement in {movement_ratio:.1%} of candles" + 
                          (" (good)" if has_movement else " (suspicious)")
            }
            
        except Exception:
            return {'passed': False, 'message': "Error checking price movement"}
    
    def _check_realistic_ohlc(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if OHLC relationships are realistic"""
        try:
            # Check basic OHLC relationships: Low <= Open,Close <= High
            valid_ohlc = (
                (df['low'] <= df['open']) & 
                (df['low'] <= df['close']) & 
                (df['open'] <= df['high']) & 
                (df['close'] <= df['high'])
            ).all()
            
            # Check for realistic spreads (high-low should not be too large or too small)
            spreads = df['high'] - df['low']
            avg_price = df['close'].mean()
            spread_ratios = spreads / avg_price
            
            # Spreads should be reasonable (between 0.001% and 1% of price)
            reasonable_spreads = ((spread_ratios >= 0.00001) & (spread_ratios <= 0.01)).mean()
            
            is_realistic = valid_ohlc and reasonable_spreads >= 0.8
            
            return {
                'passed': is_realistic,
                'message': f"OHLC relationships valid: {valid_ohlc}, Reasonable spreads: {reasonable_spreads:.1%}"
            }
            
        except Exception:
            return {'passed': False, 'message': "Error checking OHLC relationships"}
    
    def _check_volume_activity(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if volume shows realistic activity"""
        try:
            if 'volume' not in df.columns:
                return {'passed': True, 'message': "No volume data available (skipped)"}
            
            # Check for volume variation
            volume_std = df['volume'].std()
            volume_mean = df['volume'].mean()
            
            if volume_mean == 0:
                return {'passed': False, 'message': "Zero volume detected"}
            
            # Volume should show some variation (coefficient of variation > 0.1)
            cv = volume_std / volume_mean if volume_mean > 0 else 0
            has_variation = cv > 0.1
            
            return {
                'passed': has_variation,
                'message': f"Volume variation coefficient: {cv:.3f}" + 
                          (" (good)" if has_variation else " (static)")
            }
            
        except Exception:
            return {'passed': True, 'message': "Volume check skipped due to error"}
    
    def _check_time_progression(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if timestamps show proper progression"""
        try:
            if 'timestamp' not in df.columns:
                return {'passed': True, 'message': "No timestamp data available (skipped)"}
            
            # Check if timestamps are in ascending order
            timestamps = pd.to_datetime(df['timestamp'], errors='coerce')
            is_ascending = timestamps.is_monotonic_increasing
            
            return {
                'passed': is_ascending,
                'message': "Timestamps in proper order" if is_ascending else "Timestamps not in order"
            }
            
        except Exception:
            return {'passed': True, 'message': "Time progression check skipped"}
    
    def _check_price_variance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if price shows sufficient variance to indicate real market activity"""
        try:
            price_variance = df['close'].var()
            price_std = df['close'].std()
            price_mean = df['close'].mean()
            
            # Calculate coefficient of variation
            cv = price_std / price_mean if price_mean > 0 else 0
            
            # For forex pairs, expect at least some minimal variance
            has_sufficient_variance = price_variance > self.min_price_variance
            
            return {
                'passed': has_sufficient_variance,
                'message': f"Price variance: {price_variance:.8f}, CV: {cv:.6f}" + 
                          (" (sufficient)" if has_sufficient_variance else " (too low)")
            }
            
        except Exception:
            return {'passed': False, 'message': "Error checking price variance"}
    
    def _check_candle_movements(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if individual candles show realistic movements"""
        try:
            # Count candles where open != close (showing movement)
            moving_candles = (df['open'] != df['close']).sum()
            total_candles = len(df)
            movement_ratio = moving_candles / total_candles if total_candles > 0 else 0
            
            # At least 50% of candles should show some movement
            sufficient_movement = movement_ratio >= 0.5
            
            return {
                'passed': sufficient_movement,
                'message': f"Moving candles: {moving_candles}/{total_candles} ({movement_ratio:.1%})" + 
                          (" (good)" if sufficient_movement else " (too static)")
            }
            
        except Exception:
            return {'passed': False, 'message': "Error checking candle movements"}
    
    def _create_validation_result(self, is_valid: bool, asset: str, message: str, 
                                checks: Optional[Dict] = None, score: Optional[float] = None) -> Dict[str, Any]:
        """Create standardized validation result"""
        return {
            'is_valid': is_valid,
            'asset': asset,
            'message': message,
            'checks': checks or {},
            'validity_score': score or (1.0 if is_valid else 0.0),
            'timestamp': datetime.now().isoformat()
        }
    
    def log_validation_result(self, result: Dict[str, Any]) -> None:
        """Log validation result with appropriate colors"""
        asset = result['asset']
        is_valid = result['is_valid']

        if not is_valid:
            print_colored(f"❌ {asset}: Data validation failed", "ERROR")

# Global data validator instance
data_validator = DataValidator()

# Convenience functions
def validate_market_data(df: pd.DataFrame, asset: str) -> Dict[str, Any]:
    """Validate market data using global validator"""
    return data_validator.validate_market_data(df, asset)

def is_data_valid(df: pd.DataFrame, asset: str) -> bool:
    """Quick check if data is valid"""
    result = validate_market_data(df, asset)
    return result['is_valid']

def log_validation_result(result: Dict[str, Any]) -> None:
    """Log validation result using global validator"""
    data_validator.log_validation_result(result)
