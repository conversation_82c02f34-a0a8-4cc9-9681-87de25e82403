#!/usr/bin/env python3
"""
Test script for data validation and fake signal prevention
Tests the new data validation system and result evaluation fixes
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the modules
try:
    from data_validator import validate_market_data, is_data_valid, log_validation_result
    from utils import print_colored
    print_colored("✅ Data validator imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_fake_static_data():
    """Create fake data with static prices (should fail validation)"""
    # Create data where all prices are the same (fake/static)
    static_price = 1.09000
    
    df = pd.DataFrame({
        'open': [static_price] * 20,
        'high': [static_price + 0.00001] * 20,  # Minimal variation
        'low': [static_price - 0.00001] * 20,
        'close': [static_price] * 20,  # Same close price
        'volume': [1000] * 20,  # Same volume
        'timestamp': pd.date_range(start='2025-01-01', periods=20, freq='1min')
    })
    
    return df

def create_realistic_changing_data():
    """Create realistic data with price changes (should pass validation)"""
    np.random.seed(42)
    base_price = 1.09000
    
    # Generate realistic price movements
    price_changes = np.random.normal(0, 0.0002, 20)  # Realistic volatility
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(new_price)
    
    # Create OHLC data with realistic variations
    data = []
    for i in range(len(prices)):
        open_price = prices[i]
        close_price = prices[i] + np.random.normal(0, 0.0001)  # Small variation
        high_price = max(open_price, close_price) + abs(np.random.normal(0, 0.00005))
        low_price = min(open_price, close_price) - abs(np.random.normal(0, 0.00005))
        
        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000 + np.random.randint(-200, 200),  # Variable volume
            'timestamp': pd.Timestamp('2025-01-01') + pd.Timedelta(minutes=i)
        })
    
    return pd.DataFrame(data)

def create_minimal_data():
    """Create data with insufficient candles (should fail validation)"""
    df = pd.DataFrame({
        'open': [1.09000, 1.09001],
        'high': [1.09002, 1.09003],
        'low': [1.08998, 1.08999],
        'close': [1.09001, 1.09000],
        'volume': [1000, 1100],
        'timestamp': pd.date_range(start='2025-01-01', periods=2, freq='1min')
    })
    
    return df

def test_result_evaluation_logic():
    """Test the fixed result evaluation logic"""
    print_colored("\n🧪 Testing Result Evaluation Logic...", "INFO", bold=True)
    
    test_cases = [
        # (signal_price, current_price, direction, expected_result)
        (1.09000, 1.09001, "call", "win"),    # Call wins when price goes up
        (1.09000, 1.08999, "call", "loss"),   # Call loses when price goes down
        (1.09000, 1.09000, "call", "win"),    # Call wins on tie (FIXED)
        
        (1.09000, 1.08999, "put", "win"),     # Put wins when price goes down
        (1.09000, 1.09001, "put", "loss"),    # Put loses when price goes up
        (1.09000, 1.09000, "put", "win"),     # Put wins on tie (FIXED)
    ]
    
    all_passed = True
    
    for signal_price, current_price, direction, expected in test_cases:
        # Apply the fixed logic
        if direction == "call":
            result = "win" if current_price >= signal_price else "loss"
        elif direction == "put":
            result = "win" if current_price <= signal_price else "loss"
        
        if result == expected:
            print_colored(f"✅ {direction.upper()} {signal_price} → {current_price} = {result} (correct)", "SUCCESS")
        else:
            print_colored(f"❌ {direction.upper()} {signal_price} → {current_price} = {result} (expected {expected})", "ERROR")
            all_passed = False
    
    return all_passed

def test_data_validation():
    """Test data validation with different data types"""
    print_colored("\n🧪 Testing Data Validation...", "INFO", bold=True)
    
    test_cases = [
        ("USDEGP_otc (Fake Static Data)", create_fake_static_data(), False),
        ("EURUSD_otc (Realistic Data)", create_realistic_changing_data(), True),
        ("USDNGN_otc (Minimal Data)", create_minimal_data(), False),
    ]
    
    results = []
    
    for test_name, df, should_pass in test_cases:
        print_colored(f"\n📊 Testing: {test_name}", "INFO")
        
        validation_result = validate_market_data(df, test_name)
        log_validation_result(validation_result)
        
        is_valid = validation_result['is_valid']
        
        if is_valid == should_pass:
            print_colored(f"✅ {test_name}: Validation result correct", "SUCCESS")
            results.append(True)
        else:
            print_colored(f"❌ {test_name}: Expected {should_pass}, got {is_valid}", "ERROR")
            results.append(False)
    
    return all(results)

def test_fake_signal_prevention():
    """Test that fake signals are prevented"""
    print_colored("\n🧪 Testing Fake Signal Prevention...", "INFO", bold=True)
    
    # Test with static data (should be rejected)
    static_data = create_fake_static_data()
    is_static_valid = is_data_valid(static_data, "USDEGP_otc")
    
    # Test with realistic data (should be accepted)
    realistic_data = create_realistic_changing_data()
    is_realistic_valid = is_data_valid(realistic_data, "EURUSD_otc")
    
    if not is_static_valid and is_realistic_valid:
        print_colored("✅ Fake signal prevention working correctly", "SUCCESS")
        print_colored("   - Static/fake data rejected ❌", "INFO")
        print_colored("   - Realistic data accepted ✅", "INFO")
        return True
    else:
        print_colored("❌ Fake signal prevention not working correctly", "ERROR")
        print_colored(f"   - Static data valid: {is_static_valid} (should be False)", "ERROR")
        print_colored(f"   - Realistic data valid: {is_realistic_valid} (should be True)", "ERROR")
        return False

def main():
    """Run all validation tests"""
    print_colored("🚀 DATA VALIDATION AND FAKE SIGNAL PREVENTION TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 70, "SKY_BLUE")
    
    tests = [
        ("Result Evaluation Logic", test_result_evaluation_logic),
        ("Data Validation", test_data_validation),
        ("Fake Signal Prevention", test_fake_signal_prevention)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print_colored(f"\n✅ {test_name} test PASSED", "SUCCESS", bold=True)
            else:
                print_colored(f"\n❌ {test_name} test FAILED", "ERROR", bold=True)
        except Exception as e:
            print_colored(f"\n❌ {test_name} test ERROR: {e}", "ERROR", bold=True)
    
    print_colored("\n" + "=" * 70, "SKY_BLUE")
    if passed == total:
        print_colored(f"🎉 ALL TESTS PASSED ({passed}/{total})", "SUCCESS", bold=True)
        print_colored("✅ Fake signal prevention system is working!", "SUCCESS", bold=True)
        print_colored("✅ Only real market data will be used for signals!", "SUCCESS", bold=True)
    else:
        print_colored(f"⚠️ SOME TESTS FAILED ({passed}/{total})", "WARNING", bold=True)
        print_colored("❌ Please check the failed components", "ERROR")
    
    print_colored("=" * 70, "SKY_BLUE")

if __name__ == "__main__":
    main()
