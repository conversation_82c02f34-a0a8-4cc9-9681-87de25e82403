#!/usr/bin/env python3
"""
Test script for the enhanced trading bot system
Tests signal logging, market analysis, and performance tracking
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the new modules
try:
    from signal_logger import signal_logger, save_signal, get_pending_signals
    from market_analyzer import market_analyzer, analyze_market_structure, get_indicator_values
    from performance_tracker import performance_tracker, update_performance, print_performance_box
    from utils import print_colored
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_test_dataframe():
    """Create a test DataFrame with sample market data"""
    dates = pd.date_range(start='2025-01-01', periods=50, freq='1min')
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 1.09000
    price_changes = np.random.normal(0, 0.0001, 50)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 0.5))  # Ensure positive prices
    
    df = pd.DataFrame({
        'time': dates,
        'open': prices,
        'high': [p + abs(np.random.normal(0, 0.00005)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.00005)) for p in prices],
        'close': prices,
        'volume': np.random.randint(100, 1000, 50)
    })
    
    # Add some basic technical indicators
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    df['rsi'] = 50 + np.random.normal(0, 10, 50)  # Simplified RSI
    df['macd'] = df['ema_12'] - df['ema_26']
    df['bb_upper'] = df['close'] * 1.001
    df['bb_middle'] = df['close']
    df['bb_lower'] = df['close'] * 0.999
    
    return df

def test_signal_logger():
    """Test signal logging functionality"""
    print_colored("\n🧪 Testing Signal Logger...", "INFO", bold=True)
    
    try:
        # Create test signal data
        test_signal = signal_logger.create_signal_data(
            pair="EURUSD_otc",
            direction="call",
            price=1.09345,
            strategy_used="S1",
            indicators={"ema_12": 1.09340, "rsi": 65.5},
            market_analysis={"market_trend": "bullish", "support_level": 1.09320},
            timeframe="1m",
            historical_candles=20,
            data_fetch_candles=100
        )
        
        # Save signal
        success = save_signal(test_signal)
        if success:
            print_colored("✅ Signal saved successfully", "SUCCESS")
        else:
            print_colored("❌ Failed to save signal", "ERROR")
            return False
        
        # Test getting pending signals
        pending = get_pending_signals()
        if pending:
            print_colored(f"✅ Found {len(pending)} pending signal(s)", "SUCCESS")
        else:
            print_colored("⚠️ No pending signals found", "WARNING")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Signal logger test failed: {e}", "ERROR")
        return False

def test_market_analyzer():
    """Test market analysis functionality"""
    print_colored("\n🧪 Testing Market Analyzer...", "INFO", bold=True)
    
    try:
        # Create test data
        df = create_test_dataframe()
        
        # Test market structure analysis
        market_analysis = analyze_market_structure(df)
        print_colored(f"✅ Market trend: {market_analysis['market_trend']}", "SUCCESS")
        print_colored(f"✅ Support level: {market_analysis['support_level']:.5f}", "SUCCESS")
        print_colored(f"✅ Resistance level: {market_analysis['resistance_level']:.5f}", "SUCCESS")
        
        # Test indicator extraction
        indicators = get_indicator_values(df)
        if indicators:
            print_colored(f"✅ Extracted {len(indicators)} indicators", "SUCCESS")
            for key, value in list(indicators.items())[:3]:  # Show first 3
                print_colored(f"   {key}: {value}", "INFO")
        else:
            print_colored("⚠️ No indicators extracted", "WARNING")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Market analyzer test failed: {e}", "ERROR")
        return False

def test_performance_tracker():
    """Test performance tracking functionality"""
    print_colored("\n🧪 Testing Performance Tracker...", "INFO", bold=True)
    
    try:
        # Create test signal results
        test_signals = [
            {
                'pair': 'EURUSD_otc',
                'result': 'win',
                'date': '2025-07-27',
                'direction': 'call',
                'price': 1.09345,
                'final_price': 1.09350
            },
            {
                'pair': 'EURUSD_otc',
                'result': 'loss',
                'date': '2025-07-27',
                'direction': 'put',
                'price': 1.09340,
                'final_price': 1.09345
            },
            {
                'pair': 'EURGBP_otc',
                'result': 'win',
                'date': '2025-07-27',
                'direction': 'call',
                'price': 0.85123,
                'final_price': 0.85130
            }
        ]
        
        # Update performance with test data
        for signal in test_signals:
            success = update_performance(signal)
            if not success:
                print_colored("❌ Failed to update performance", "ERROR")
                return False
        
        print_colored("✅ Performance data updated successfully", "SUCCESS")
        
        # Test performance summary display
        test_pairs = ['EURUSD_otc', 'EURGBP_otc']
        print_colored("\n📊 Testing Performance Summary Display:", "INFO")
        print_performance_box(test_pairs)
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Performance tracker test failed: {e}", "ERROR")
        return False

def main():
    """Run all tests"""
    print_colored("🚀 ENHANCED TRADING BOT SYSTEM TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 60, "SKY_BLUE")
    
    tests = [
        ("Signal Logger", test_signal_logger),
        ("Market Analyzer", test_market_analyzer),
        ("Performance Tracker", test_performance_tracker)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print_colored(f"✅ {test_name} test PASSED", "SUCCESS")
            else:
                print_colored(f"❌ {test_name} test FAILED", "ERROR")
        except Exception as e:
            print_colored(f"❌ {test_name} test ERROR: {e}", "ERROR")
    
    print_colored("\n" + "=" * 60, "SKY_BLUE")
    if passed == total:
        print_colored(f"🎉 ALL TESTS PASSED ({passed}/{total})", "SUCCESS", bold=True)
        print_colored("✅ Enhanced trading bot system is ready!", "SUCCESS", bold=True)
    else:
        print_colored(f"⚠️ SOME TESTS FAILED ({passed}/{total})", "WARNING", bold=True)
        print_colored("❌ Please check the failed components", "ERROR")
    
    print_colored("=" * 60, "SKY_BLUE")

if __name__ == "__main__":
    main()
