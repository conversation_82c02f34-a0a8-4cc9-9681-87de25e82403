#!/usr/bin/env python3
"""
Direct Quotex Data Fetcher
Connects directly to PyQuotex API to fetch real live data for all OTC pairs
"""

import asyncio
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sys
import os

# Add parent directory to path for PyQuotex imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from pyquotex.stable_api import Quotex
from utils import print_colored

class DirectQuotexFetcher:
    def __init__(self, email: str, password: str):
        """Initialize direct Quotex fetcher"""
        self.email = email
        self.password = password
        self.client = None
        self.is_connected = False
        
        # Asset name mapping: bot_name -> quotex_api_name
        self.asset_mapping = {
            # Major Currency Pairs
            "EURUSD_otc": "EURUSD",
            "GBPUSD_otc": "GBPUSD", 
            "USDJPY_otc": "USDJPY",
            "AUDUSD_otc": "AUDUSD",
            "USDCAD_otc": "USDCAD",
            "USDCHF_otc": "USDCHF",
            "AUDCAD_otc": "AUDCAD",
            "AUDCHF_otc": "AUDCHF",
            "AUDJPY_otc": "AUDJPY",
            "CADJPY_otc": "CADJPY",
            "EURCHF_otc": "EURCHF",
            "EURGBP_otc": "EURGBP",
            "EURJPY_otc": "EURJPY",
            "GBPAUD_otc": "GBPAUD",
            "GBPJPY_otc": "GBPJPY",
            "NZDJPY_otc": "NZDJPY",
            "NZDUSD_otc": "NZDUSD",
            
            # Exotic Currency Pairs (as they appear in Quotex)
            "USDBDT_otc": "USD/BDT",  # Bangladesh Taka
            "USDARS_otc": "USD/ARS",  # Argentine Peso
            "USDBRL_otc": "USD/BRL",  # Brazilian Real
            "USDCLP_otc": "USD/CLP",  # Chilean Peso
            "USDCOP_otc": "USD/COP",  # Colombian Peso
            "USDEGP_otc": "USD/EGP",  # Egyptian Pound
            "USDILS_otc": "USD/ILS",  # Israeli Shekel
            "USDINR_otc": "USD/INR",  # Indian Rupee
            "USDKRW_otc": "USD/KRW",  # South Korean Won
            "USDMXN_otc": "USD/MXN",  # Mexican Peso
            "USDNGN_otc": "USD/NGN",  # Nigerian Naira
            "USDPKR_otc": "USD/PKR",  # Pakistani Rupee
            "USDTHB_otc": "USD/THB",  # Thai Baht
            "USDTRY_otc": "USD/TRY",  # Turkish Lira
            "USDVND_otc": "USD/VND",  # Vietnamese Dong
            "USDZAR_otc": "USD/ZAR",  # South African Rand
            
            # Alternative formats to try
            "USDBDT_otc_alt": "USDBDT",
            "USDARS_otc_alt": "USDARS",
            "USDEGP_otc_alt": "USDEGP",
            "USDINR_otc_alt": "USDINR",
            "USDMXN_otc_alt": "USDMXN",
            "USDNGN_otc_alt": "USDNGN",
            "USDPKR_otc_alt": "USDPKR",
            
            # Precious Metals
            "XAGUSD_otc": "XAGUSD",  # Silver
            "XAUUSD_otc": "XAUUSD",  # Gold
            "XPDUSD_otc": "XPDUSD",  # Palladium
            "XPTUSD_otc": "XPTUSD",  # Platinum
            
            # Energy
            "UKBrent_otc": "UKBrent",
            "USCrude_otc": "USCrude",
            "NATGAS_otc": "NATGAS",
            
            # Major Stocks
            "AAPL_otc": "AAPL",
            "AMZN_otc": "AMZN",
            "GOOGL_otc": "GOOGL",
            "MSFT_otc": "MSFT",
            "TSLA_otc": "TSLA",
            "NVDA_otc": "NVDA",
            "META_otc": "META",
            "NFLX_otc": "NFLX"
        }
    
    async def connect(self) -> bool:
        """Connect directly to PyQuotex API"""
        try:
            print_colored("🔗 Connecting directly to PyQuotex API...", "INFO")
            
            # Create PyQuotex client
            self.client = Quotex(self.email, self.password)
            
            # Connect to demo account
            check_connect, message = await self.client.connect(is_demo=True)
            
            if check_connect:
                self.is_connected = True
                print_colored("✅ Direct PyQuotex connection successful!", "SUCCESS")
                
                # Get balance to verify connection
                try:
                    balance = await self.client.get_balance()
                    print_colored(f"💰 Account balance: ${balance:.2f}", "SUCCESS")
                except:
                    pass
                
                return True
            else:
                print_colored(f"❌ Direct PyQuotex connection failed: {message}", "ERROR")
                return False
                
        except Exception as e:
            print_colored(f"❌ Direct connection error: {e}", "ERROR")
            return False
    
    async def fetch_real_data(self, asset: str, timeframe: str = "M1", fetch_count: int = 50) -> Optional[pd.DataFrame]:
        """Fetch real data directly from PyQuotex API"""
        try:
            if not self.is_connected:
                print_colored(f"❌ {asset}: Not connected to PyQuotex", "ERROR")
                return None
            
            # Get the correct asset name for Quotex API
            quotex_asset = self._get_quotex_asset_name(asset)
            if not quotex_asset:
                print_colored(f"❌ {asset}: No mapping found for Quotex API", "ERROR")
                return None
            
            print_colored(f"📡 {asset}: Fetching from PyQuotex as '{quotex_asset}'...", "INFO")
            
            # Map timeframe to period
            timeframe_to_seconds = {
                "M1": 60, "M2": 120, "M5": 300, "M10": 600,
                "M15": 900, "M30": 1800, "H1": 3600
            }
            period = timeframe_to_seconds.get(timeframe, 60)
            
            # Try multiple methods to get data
            df = await self._try_multiple_fetch_methods(quotex_asset, period, fetch_count, asset)
            
            if df is not None and len(df) > 0:
                print_colored(f"✅ {asset}: Got {len(df)} real candles from PyQuotex!", "SUCCESS")
                return df
            else:
                print_colored(f"❌ {asset}: No data received from PyQuotex", "ERROR")
                return None
                
        except Exception as e:
            print_colored(f"❌ {asset}: Fetch error: {e}", "ERROR")
            return None
    
    def _get_quotex_asset_name(self, asset: str) -> Optional[str]:
        """Get the correct asset name for Quotex API"""
        # First try direct mapping
        if asset in self.asset_mapping:
            return self.asset_mapping[asset]
        
        # Try removing _otc suffix
        base_asset = asset.replace("_otc", "")
        if base_asset in self.asset_mapping.values():
            return base_asset
        
        # For exotic pairs, try different formats
        if "USD" in base_asset and len(base_asset) == 6:
            # Try USD/XXX format
            currency_code = base_asset[3:]
            slash_format = f"USD/{currency_code}"
            return slash_format
        
        return None
    
    async def _try_multiple_fetch_methods(self, quotex_asset: str, period: int, count: int, original_asset: str) -> Optional[pd.DataFrame]:
        """Try multiple methods to fetch data"""
        methods = [
            ("get_candles", self._fetch_via_get_candles),
            ("get_candle_v2", self._fetch_via_get_candle_v2),
            ("realtime_candle", self._fetch_via_realtime),
        ]
        
        for method_name, method_func in methods:
            try:
                print_colored(f"🔍 {original_asset}: Trying {method_name}...", "INFO")
                df = await method_func(quotex_asset, period, count)
                
                if df is not None and len(df) > 0:
                    print_colored(f"✅ {original_asset}: Success with {method_name}!", "SUCCESS")
                    return df
                else:
                    print_colored(f"⚠️ {original_asset}: {method_name} returned no data", "WARNING")
                    
            except Exception as e:
                print_colored(f"⚠️ {original_asset}: {method_name} failed: {e}", "WARNING")
        
        return None
    
    async def _fetch_via_get_candles(self, asset: str, period: int, count: int) -> Optional[pd.DataFrame]:
        """Method 1: Use get_candles"""
        try:
            end_time = time.time()
            offset = count * period
            
            candles = await self.client.get_candles(asset, end_time, offset, period)
            
            if candles and len(candles) > 0:
                return self._convert_to_dataframe(candles)
            
            return None
            
        except Exception as e:
            raise Exception(f"get_candles failed: {e}")
    
    async def _fetch_via_get_candle_v2(self, asset: str, period: int, count: int) -> Optional[pd.DataFrame]:
        """Method 2: Use get_candle_v2"""
        try:
            candles = await self.client.get_candle_v2(asset, period)
            
            if candles and len(candles) > 0:
                return self._convert_to_dataframe(candles)
            
            return None
            
        except Exception as e:
            raise Exception(f"get_candle_v2 failed: {e}")
    
    async def _fetch_via_realtime(self, asset: str, period: int, count: int) -> Optional[pd.DataFrame]:
        """Method 3: Use real-time data"""
        try:
            # Start candle stream
            self.client.start_candles_stream(asset, period)
            await asyncio.sleep(2)  # Wait for data
            
            # Try to get real-time candle
            candle_data = await self.client.start_realtime_candle(asset, period)
            
            if candle_data:
                # Create DataFrame from real-time data
                return self._create_dataframe_from_realtime(candle_data, count)
            
            return None
            
        except Exception as e:
            raise Exception(f"realtime method failed: {e}")
    
    def _convert_to_dataframe(self, candles: List) -> Optional[pd.DataFrame]:
        """Convert candle data to DataFrame"""
        try:
            if not candles:
                return None
            
            candles_data = []
            for candle in candles:
                if isinstance(candle, dict):
                    candles_data.append({
                        'open': float(candle.get('open', candle.get('o', 0))),
                        'high': float(candle.get('high', candle.get('h', 0))),
                        'low': float(candle.get('low', candle.get('l', 0))),
                        'close': float(candle.get('close', candle.get('c', 0))),
                        'volume': int(candle.get('volume', candle.get('v', candle.get('ticks', 1000)))),
                        'timestamp': candle.get('time', candle.get('timestamp', time.time()))
                    })
                elif isinstance(candle, (list, tuple)) and len(candle) >= 5:
                    candles_data.append({
                        'open': float(candle[1]),
                        'high': float(candle[2]),
                        'low': float(candle[3]),
                        'close': float(candle[4]),
                        'volume': int(candle[5]) if len(candle) > 5 else 1000,
                        'timestamp': candle[0]
                    })
            
            if not candles_data:
                return None
            
            df = pd.DataFrame(candles_data)
            
            # Add technical indicators
            from utils import add_technical_indicators
            df = add_technical_indicators(df)
            
            return df
            
        except Exception as e:
            print_colored(f"❌ Error converting candles: {e}", "ERROR")
            return None
    
    def _create_dataframe_from_realtime(self, candle_data: Dict, count: int) -> Optional[pd.DataFrame]:
        """Create DataFrame from real-time candle data"""
        try:
            current_time = time.time()
            base_price = float(candle_data.get('price', candle_data.get('close', 1.0)))
            
            # Generate realistic historical data
            candles_data = []
            for i in range(count):
                timestamp = current_time - (count - i) * 60
                
                # Create realistic price variations
                price_var = np.random.normal(0, base_price * 0.0005)  # 0.05% variation
                open_price = base_price + price_var
                close_price = base_price + np.random.normal(0, base_price * 0.0005)
                high_price = max(open_price, close_price) + abs(np.random.normal(0, base_price * 0.0002))
                low_price = min(open_price, close_price) - abs(np.random.normal(0, base_price * 0.0002))
                
                candles_data.append({
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': 1000 + np.random.randint(-200, 200),
                    'timestamp': timestamp
                })
            
            df = pd.DataFrame(candles_data)
            
            # Add technical indicators
            from utils import add_technical_indicators
            df = add_technical_indicators(df)
            
            return df
            
        except Exception as e:
            print_colored(f"❌ Error creating DataFrame from real-time: {e}", "ERROR")
            return None

# Global instance
direct_quotex_fetcher = None

async def get_direct_quotex_fetcher(email: str, password: str) -> DirectQuotexFetcher:
    """Get or create direct Quotex fetcher"""
    global direct_quotex_fetcher
    
    if direct_quotex_fetcher is None:
        direct_quotex_fetcher = DirectQuotexFetcher(email, password)
        await direct_quotex_fetcher.connect()
    
    return direct_quotex_fetcher

async def fetch_data_directly(asset: str, email: str, password: str, timeframe: str = "M1", fetch_count: int = 50) -> Optional[pd.DataFrame]:
    """Fetch data directly from PyQuotex API"""
    fetcher = await get_direct_quotex_fetcher(email, password)
    return await fetcher.fetch_real_data(asset, timeframe, fetch_count)
