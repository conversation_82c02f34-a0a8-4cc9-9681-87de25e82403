#!/usr/bin/env python3
"""
Signal Logger Module for Trading Bot
Handles real-time signal logging, result tracking, and file storage
"""

import os
import json
import csv
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from utils import print_colored

# Global storage for active signals
active_signals: List[Dict[str, Any]] = []

class SignalLogger:
    def __init__(self, signals_dir: str = "signals"):
        """Initialize signal logger with storage directory"""
        self.signals_dir = signals_dir
        self.ensure_signals_directory()
    
    def ensure_signals_directory(self):
        """Create signals directory if it doesn't exist"""
        if not os.path.exists(self.signals_dir):
            os.makedirs(self.signals_dir)
            print_colored(f"📁 Created signals directory: {self.signals_dir}", "INFO")
    
    def create_signal_data(self, pair: str, direction: str, price: float, 
                          strategy_used: str, indicators: Dict[str, float],
                          market_analysis: Dict[str, Any], timeframe: str,
                          historical_candles: int, data_fetch_candles: int) -> Dict[str, Any]:
        """Create comprehensive signal data structure"""
        now = datetime.now()
        
        signal_data = {
            'pair': pair,
            'timestamp': now.strftime('%H:%M:%S'),
            'date': now.strftime('%Y-%m-%d'),
            'direction': direction,
            'price': price,
            'historical_candles': historical_candles,
            'data_fetch_candles': data_fetch_candles,
            'strategy_used': strategy_used,
            'indicators': indicators,
            'timeframe': timeframe,
            'market_analysis': market_analysis,
            'result': 'pending',
            'final_price': None,
            'evaluation_timestamp': None,
            'created_at': now.isoformat()
        }
        
        return signal_data
    
    def save_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Save signal to memory and daily file"""
        try:
            # Add to active signals memory
            active_signals.append(signal_data.copy())
            
            # Save to daily file
            self._save_to_daily_file(signal_data)
            

            return True
            
        except Exception as e:
            print_colored(f"❌ Error saving signal: {e}", "ERROR")
            return False
    
    def _save_to_daily_file(self, signal_data: Dict[str, Any]):
        """Save signal to daily JSON file"""
        date = signal_data['date']
        filename = f"signals_{date}.json"
        filepath = os.path.join(self.signals_dir, filename)
        
        # Load existing signals for the day
        daily_signals = self.load_daily_signals(date)
        
        # Add new signal
        daily_signals.append(signal_data)
        
        # Save back to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(daily_signals, f, indent=2, ensure_ascii=False)
    
    def load_daily_signals(self, date: str) -> List[Dict[str, Any]]:
        """Load existing signals for a specific date"""
        filename = f"signals_{date}.json"
        filepath = os.path.join(self.signals_dir, filename)
        
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print_colored(f"❌ Error loading daily signals: {e}", "ERROR")
                return []
        return []
    
    def update_signal_result(self, pair: str, timestamp: str, result: str, 
                           final_price: float) -> bool:
        """Update signal result in memory and file"""
        try:
            updated = False
            evaluation_time = datetime.now()
            
            # Update in active signals memory
            for signal in active_signals:
                if (signal['pair'] == pair and 
                    signal['timestamp'] == timestamp and 
                    signal['result'] == 'pending'):
                    
                    signal['result'] = result
                    signal['final_price'] = final_price
                    signal['evaluation_timestamp'] = evaluation_time.strftime('%H:%M:%S')
                    updated = True
                    break
            
            if updated:
                # Update in daily file
                date = evaluation_time.strftime('%Y-%m-%d')
                daily_signals = self.load_daily_signals(date)
                
                for signal in daily_signals:
                    if (signal['pair'] == pair and 
                        signal['timestamp'] == timestamp and 
                        signal['result'] == 'pending'):
                        
                        signal['result'] = result
                        signal['final_price'] = final_price
                        signal['evaluation_timestamp'] = evaluation_time.strftime('%H:%M:%S')
                        break
                
                # Save updated signals
                filename = f"signals_{date}.json"
                filepath = os.path.join(self.signals_dir, filename)
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(daily_signals, f, indent=2, ensure_ascii=False)
                

                return True
            
            return False
            
        except Exception as e:
            print_colored(f"❌ Error updating signal result: {e}", "ERROR")
            return False
    
    def get_pending_signals(self, pair: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get pending signals for evaluation"""
        pending = [s for s in active_signals if s['result'] == 'pending']
        
        if pair:
            pending = [s for s in pending if s['pair'] == pair]
        
        return pending
    
    def cleanup_old_signals(self, hours_old: int = 24):
        """Remove old signals from memory to prevent memory leaks"""
        global active_signals
        cutoff_time = datetime.now() - timedelta(hours=hours_old)
        
        original_count = len(active_signals)
        active_signals = [
            s for s in active_signals 
            if datetime.fromisoformat(s['created_at']) > cutoff_time
        ]
        
        cleaned_count = original_count - len(active_signals)
        if cleaned_count > 0:
            print_colored(f"🧹 Cleaned {cleaned_count} old signals from memory", "INFO")
    
    def export_to_csv(self, date: str) -> bool:
        """Export daily signals to CSV format"""
        try:
            daily_signals = self.load_daily_signals(date)
            if not daily_signals:
                return False
            
            csv_filename = f"signals_{date}.csv"
            csv_filepath = os.path.join(self.signals_dir, csv_filename)
            
            with open(csv_filepath, 'w', newline='', encoding='utf-8') as f:
                if daily_signals:
                    # Flatten the nested dictionaries for CSV
                    flattened_signals = []
                    for signal in daily_signals:
                        flat_signal = signal.copy()
                        
                        # Flatten indicators
                        if 'indicators' in signal:
                            for key, value in signal['indicators'].items():
                                flat_signal[f'indicator_{key}'] = value
                            del flat_signal['indicators']
                        
                        # Flatten market_analysis
                        if 'market_analysis' in signal:
                            for key, value in signal['market_analysis'].items():
                                flat_signal[f'market_{key}'] = value
                            del flat_signal['market_analysis']
                        
                        flattened_signals.append(flat_signal)
                    
                    writer = csv.DictWriter(f, fieldnames=flattened_signals[0].keys())
                    writer.writeheader()
                    writer.writerows(flattened_signals)
            
            print_colored(f"📊 Exported signals to CSV: {csv_filename}", "SUCCESS")
            return True
            
        except Exception as e:
            print_colored(f"❌ Error exporting to CSV: {e}", "ERROR")
            return False

# Global signal logger instance
signal_logger = SignalLogger()

# Convenience functions for easy access
def save_signal(signal_data: Dict[str, Any]) -> bool:
    """Save signal using global logger instance"""
    return signal_logger.save_signal(signal_data)

def update_signal_result(pair: str, timestamp: str, result: str, final_price: float) -> bool:
    """Update signal result using global logger instance"""
    return signal_logger.update_signal_result(pair, timestamp, result, final_price)

def get_pending_signals(pair: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get pending signals using global logger instance"""
    return signal_logger.get_pending_signals(pair)

def cleanup_old_signals(hours_old: int = 24):
    """Cleanup old signals using global logger instance"""
    signal_logger.cleanup_old_signals(hours_old)
