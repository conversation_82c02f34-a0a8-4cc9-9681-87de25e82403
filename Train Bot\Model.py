#!/usr/bin/env python3
"""
Quotex Trading Bot Launcher - Updated with PyQuotex Integration
Comprehensive trading bot with working PyQuotex integration
Owner: Muhammad <PERSON>
Model: 2.0.0 
"""

import sys
import os
import time
import asyncio
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from contextlib import redirect_stderr
from io import StringIO
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

class SuppressOutput:
    """Context manager to suppress stdout and stderr"""
    def __enter__(self):
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr
        sys.stdout = StringIO()
        sys.stderr = StringIO()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self._original_stdout
        sys.stderr = self._original_stderr
        return True  # Suppress any exceptions

# Import PyQuotex integration (Browser-based with real data validation)
try:
    from quotex_integration import QuotexBotIntegration, get_quotex_client
    QUOTEX_AVAILABLE = True
    print("✅ PyQuotex integration loaded successfully")
except ImportError as e:
    print(f"❌ PyQuotex integration not found: {e}")
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price, fetch_live_candles, get_timeframe_time_info
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, OANDA_CONFIG, TIMEFRAME_CONFIG

# Import new enhanced modules
from signal_logger import signal_logger, save_signal, update_signal_result, get_pending_signals, cleanup_old_signals
from market_analyzer import market_analyzer, analyze_market_structure, get_indicator_values
from performance_tracker import performance_tracker, update_performance, print_performance_box, reset_session_performance
from data_validator import data_validator, validate_market_data, is_data_valid, log_validation_result
from advance_signal_analyzer import run_advance_signal_analysis
# Remove OTC-related imports - LIVE PAIRS ONLY

# Quotex credentials and URLs (Updated credentials)
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"
QUOTEX_LIVE_URL = "https://market-qx.pro/en/trade"  # Official Quotex URL
QUOTEX_DEMO_URL = "https://market-qx.pro/en/demo-trade"

# LIVE PAIRS ONLY - NO OTC PAIRS FOR REAL MONEY TRADING
LIVE_PAIRS_ONLY = [
    # Major Currency Pairs
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",

    # Cross Currency Pairs (including new ones as requested)
    "EURGBP", "EURJPY", "GBPJPY",
    "AUDJPY",  # New - Added as requested
    "CADJPY",  # New - Added as requested
    "CHFJPY",  # New - Added as requested
    "AUDCAD",  # New - Added as requested

    # Additional Cross Pairs
    "EURAUD", "GBPAUD", "AUDCHF", "EURCHF", "GBPCHF", "GBPCAD", "NZDJPY",
    "USDSGD", "AUDSGD"
]

# Available timeframes for Quotex
QUOTEX_TIMEFRAMES = {
    "1": {"name": "1 Minute", "seconds": 60},
    "2": {"name": "2 Minutes", "seconds": 120},
    "5": {"name": "5 Minutes", "seconds": 300},
    "10": {"name": "10 Minutes", "seconds": 600},
    "15": {"name": "15 Minutes", "seconds": 900},
    "30": {"name": "30 Minutes", "seconds": 1800},
    "60": {"name": "1 Hour", "seconds": 3600}
}

# Global Quotex client
quotex_client = None

def show_menu():
    """Display the main menu"""
    print_header("🚀 QUOTEX TRADING BOT SYSTEM")
    print_colored("Choose an option:", "SKY_BLUE", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "SUCCESS", bold=True)
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "DARK_MULBERRY", bold=True)
    print_colored("3. 💰 Quotex Live (Live trading)", "BURNT_ORANGE", bold=True)
    print_colored("4. � Advance Signal Analysis", "PURPLE", bold=True)
    print_colored("5. �💳 Check Quotex Balance", "TROPICAL_RAINFOREST", bold=True)
    print_colored("6. ❌ Exit", "ERROR", bold=True)
    print()

async def check_quotex_balance():
    """Check and display Quotex account balance using direct API"""
    print_header("💳 QUOTEX BALANCE CHECK")

    if not QUOTEX_AVAILABLE:
        print_colored("❌ Direct PyQuotex API not available", "ERROR")
        return

    try:
        print_colored("🔗 Connecting to Quotex directly...", "SUCCESS")

        # Create browser-based client
        client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=True)

        # Connect
        connected = await client.connect()

        if connected:
            print_colored("✅ Connected to Quotex successfully", "SUCCESS")

            # Get current balance
            try:
                current_balance = await client.get_balance()
                print_colored(f"💰 Current balance: ${current_balance:.2f}", "SUCCESS" if current_balance > 0 else "WARNING")
            except Exception as e:
                print_colored(f"⚠️ Balance check failed: {e}", "WARNING")

        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")

    print()
    input("Press Enter to continue...")

async def connect_to_quotex(account_type="PRACTICE", max_retries=5):
    """Connect to Quotex using direct PyQuotex API - GUARANTEED REAL DATA"""
    global quotex_client

    if not QUOTEX_AVAILABLE:
        print_colored("❌ Direct PyQuotex API not available", "ERROR")
        return False

    try:
        print_colored("🔐 Connecting directly to Quotex API...", "INFO")

        # Create browser-based client
        quotex_client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=(account_type in ["PRACTICE", "DEMO"]))

        # Connect
        connected = await quotex_client.connect()

        if connected:
            print_colored("✅ Browser-based Quotex connection successful!", "SUCCESS")

            # Check connection status
            if hasattr(quotex_client, 'check_connect'):
                print_colored(f"🔗 Connection status: {quotex_client.check_connect}", "INFO")

            # Get and display balance
            try:
                balance = await quotex_client.get_balance()
                print_colored(f"💰 Current balance: ${balance:.2f}", "SUCCESS")
            except Exception as e:
                print_colored(f"⚠️ Balance check failed: {e}", "WARNING")

            # Live pairs only - no OTC mapping needed
            print_colored("✅ Live pairs mode - using direct Oanda mapping", "SUCCESS")

            # Show practice mode message
            if account_type == "PRACTICE":
                print_colored("📡 REAL DATA MODE: Using actual Quotex asset names", "PEACH", bold=True)
                print_colored("📊 Practice mode: Signal display only (using REAL validated data)", "GOLD")

            return True
        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")
            return False

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")
        return False

async def check_balance():
    """Check current account balance"""
    if not quotex_client:
        return 0

    try:
        balance = await quotex_client.get_balance()
        return balance if balance else 0
    except Exception as e:
        print_colored(f"❌ Error checking balance: {str(e)}", "ERROR")
        return 0

async def set_initial_trade_amount(amount):
    """Set trade amount immediately after connection establishment"""
    if not quotex_client:
        print_colored("❌ Quotex client not available for amount setting", "ERROR")
        return False

    try:
        success = await quotex_client._set_trade_amount_immediate(amount)

        if success:
            return True
        else:
            return False

    except Exception as e:
        return False

async def set_initial_trade_time(time_str):
    """Set trade time immediately after time selection"""
    if not quotex_client:
        print_colored("❌ Quotex client not available for time setting", "ERROR")
        return False

    try:
        success = await quotex_client._set_trade_time_immediate(time_str)

        if success:
            return True
        else:
            return False

    except Exception as e:
        return False

async def execute_trade(asset, action, amount, duration):
    """Execute trade on Quotex with clean output"""
    if not quotex_client:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

    try:
        # Execute trade (amount should already be set)
        success, trade_info = await quotex_client.trade(action, amount, asset, duration)
        return success, trade_info

    except Exception as e:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

def display_pairs_in_columns(pairs, title, columns=4, start_index=0):
    """Display trading pairs in specified number of columns"""
    print_colored(f"\n{title}", "OCEAN", bold=True)
    print_colored("=" * 80, "SKY_BLUE")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<18}"
        print_colored(formatted_row, "ROSEWOOD")

    print_colored("=" * 80, "SKY_BLUE")

def select_trading_pairs():
    """Select multiple trading pairs from available options - LIVE PAIRS ONLY"""
    print_header("💱 LIVE PAIRS SELECTION")
    print_colored("🔥 LIVE PAIRS", "SUCCESS", bold=True)
    print()

    # Display Live pairs only
    display_pairs_in_columns(LIVE_PAIRS_ONLY, "🌍 Live Currency Pairs (Real Oanda Data):", start_index=0)

    total_pairs = len(LIVE_PAIRS_ONLY)
    all_pairs = LIVE_PAIRS_ONLY

    print_colored(f"\n🔸 Select pairs (1,2,3 or 'all' for all {total_pairs} live pairs):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input(f"\nSelect assets (1-{total_pairs}): ").strip().lower()
            if not selection:
                return None

            if selection == 'all':
                selected_pairs = all_pairs.copy()
                break

            # Parse selection
            selected_pairs = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    # Range selection (e.g., 1-4)
                    start, end = map(int, part.split('-'))
                    for i in range(start-1, min(end, total_pairs)):
                        if i >= 0:
                            selected_pairs.append(all_pairs[i])
                else:
                    # Single selection
                    num = int(part)
                    if 1 <= num <= total_pairs:
                        selected_pairs.append(all_pairs[num-1])
                    else:
                        raise ValueError(f"Invalid asset number: {num}")

            # Remove duplicates while preserving order
            selected_pairs = list(dict.fromkeys(selected_pairs))

            if selected_pairs:
                break
            else:
                print_colored("❌ Please select at least one asset", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected pairs
    print_colored(f"\n✅ Selected {len(selected_pairs)} assets:", "SUCCESS", bold=True)
    for pair in selected_pairs:
        print_colored(f"   • {pair}", "SUCCESS")

    return selected_pairs

def select_timeframe():
    """Select trading timeframe"""
    print_header("⏰ TIMEFRAME SELECTION")

    print_colored("Available timeframes:", "OCEAN", bold=True)
    for key, info in QUOTEX_TIMEFRAMES.items():
        print_colored(f"{key}. {info['name']}", "ROSEWOOD")

    print_colored("\nSelect timeframe (1-7):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTimeframe: ").strip()
            if choice in QUOTEX_TIMEFRAMES:
                selected = QUOTEX_TIMEFRAMES[choice]
                print_colored(f"✅ Selected: {selected['name']}", "SUCCESS")
                return int(choice) * 60  # Return duration in seconds
            else:
                print_colored("❌ Please enter a valid timeframe number", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_time():
    """Select trade time (optional)"""
    print_header("⏰ TRADE TIME SELECTION")

    print_colored("Set trade time (optional):", "OCEAN", bold=True)
    print_colored("Format: HH:MM:SS", "TYRIAN_PURPLE")
    print_colored("Press Enter to keep default time on site", "TYRIAN_PURPLE")

    while True:
        try:
            time_input = input("\nTrade time (HH:MM:SS or Enter for default): ").strip()

            if not time_input:
                print_colored("✅ Using default time from site", "SUCCESS")
                return None

            # Validate time format
            time_parts = time_input.split(':')
            if len(time_parts) != 3:
                print_colored("❌ Invalid format. Use HH:MM:SS", "ERROR")
                continue

            try:
                hours = int(time_parts[0])
                minutes = int(time_parts[1])
                seconds = int(time_parts[2])

                # Validate ranges
                if not (0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59):
                    print_colored("❌ Invalid time values. Hours: 0-23, Minutes/Seconds: 0-59", "ERROR")
                    continue

                # Convert to total seconds for validation
                total_seconds = hours * 3600 + minutes * 60 + seconds

                # Format time first so we can use it in messages
                formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                # Allow any time including less than 1 minute (e.g., 00:00:55)
                if total_seconds < 30:
                    print_colored(f"⚠️ Time {formatted_time} is very short - minimum recommended is 30 seconds", "WARNING")
                elif total_seconds < 60:
                    print_colored(f"✅ Time {formatted_time} accepted (less than 1 minute)", "INFO")

                if total_seconds > 3600:
                    print_colored("❌ Maximum trade time is 1 hour (01:00:00)", "ERROR")
                    continue

                print_colored(f"✅ Trade time: {formatted_time}", "SUCCESS")
                return formatted_time

            except ValueError:
                print_colored("❌ Invalid time format. Use numbers only", "ERROR")
                continue

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_amount():
    """Select trade amount"""
    print_header("💰 TRADE AMOUNT SELECTION")

    print_colored("Available trade amounts:", "OCEAN", bold=True)
    amounts = ["1", "2", "5", "10", "20", "50", "100"]
    for i, amount in enumerate(amounts, 1):
        print_colored(f"{i}. ${amount}", "ROSEWOOD")
    print_colored(f"{len(amounts) + 1}. Custom Amount", "ROSEWOOD")

    print_colored("\nSelect trade amount (1,2,3 or 'custom' for custom amount):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTrade amount: ").strip().lower()
            if not choice:
                return None

            if choice.isdigit() and 1 <= int(choice) <= len(amounts):
                amount = float(amounts[int(choice) - 1])
                # Convert to int if it's a whole number (fix decimal issue)
                if amount.is_integer():
                    amount = int(amount)
                print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                return amount
            elif choice.isdigit() and int(choice) == len(amounts) + 1:
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        # Convert to int if it's a whole number (fix decimal issue)
                        if amount.is_integer():
                            amount = int(amount)
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            elif choice == 'custom':
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        # Convert to int if it's a whole number (fix decimal issue)
                        if amount.is_integer():
                            amount = int(amount)
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            else:
                try:
                    amount = float(choice)
                    if amount < 1:
                        print_colored("❌ Minimum trade amount is $1", "ERROR")
                        continue
                    elif amount > 1000:
                        print_colored("❌ Maximum trade amount is $1000", "ERROR")
                        continue
                    # Convert to int if it's a whole number (fix decimal issue)
                    if amount.is_integer():
                        amount = int(amount)
                    print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                    return amount
                except ValueError:
                    print_colored("❌ Please enter a valid number or selection", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_data_requirement():
    """Interactive data requirement selection"""
    print_header("📊 DATA REQUIREMENT SELECTION")
    print_colored("Select minimum historical candles needed to generate signals:", "OCEAN", bold=True)
    print_colored("• More candles means higher accuracy but slower processing", "GOLD")
    print_colored("• Fewer candles means Faster processing but may reduce accuracy", "GOLD")
    print()

    print_colored("Available options:", "OCEAN", bold=True)
    print_colored("  10-20 candles:   Quick analysis (basic indicators)", "TYRIAN_PURPLE")
    print_colored("  25-35 candles:  Professional analysis (recommended for real trading)", "TYRIAN_PURPLE")
    print_colored("  40-50 candles:  Maximum accuracy (institutional level)", "TYRIAN_PURPLE")
    print()
    print_colored("💱Default: 50 candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter historical candles (10 to 50) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored("✅ Selected: 50 historical candles", "SUCCESS")
                return 50

            candles = int(user_input)
            if 10 <= candles <= 50:
                print_colored(f"✅ Selected: {candles} historical candles", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 10 and 50", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 50

def select_data_fetch():
    """Interactive data fetch selection - ENHANCED FOR REAL TRADING"""
    print_header("📈 DATA FETCH SELECTION")
    print_colored("Select number of recent candles to fetch from Oanda API:", "OCEAN", bold=True)
    print_colored("• More candles = Better market context and recent price action", "GOLD")
    print_colored("• Fewer candles = Faster fetching but less market context", "GOLD")
    print_colored("💰 For REAL MONEY trading, use 400+ candles for best context", "WARNING", bold=True)
    print()

    print_colored("Available options:", "OCEAN", bold=True)
    print_colored("  50-70 candles:  Quick fetching (basic context)", "TYRIAN_PURPLE")
    print_colored("  80-120 candles:  Professional fetching (recommended for real trading)", "TYRIAN_PURPLE")
    print_colored("  130-200 candles: Maximum context (institutional level)", "TYRIAN_PURPLE")
    print()
    print_colored("⚠️  Default: 100 candles (Press Enter for default)", "OCEAN", bold=True)
    print()

    while True:
        try:
            user_input = input("Enter candles to fetch (50-200) or press Enter for default: ").strip()

            if not user_input:  # Default
                print_colored("✅ Selected: 100 candles fetch", "SUCCESS")
                return 100

            candles = int(user_input)
            if 50 <= candles <= 200:
                print_colored(f"✅ Selected: {candles} candles fetch", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 50 and 200", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 100

def select_strategies():
    """Select trading strategies"""
    print_header("🎯 STRATEGY SELECTION")

    print_colored("Available strategies:", "OCEAN", bold=True)
    strategies = list(STRATEGY_CONFIG.keys())

    # Display strategies in two columns
    for i in range(0, len(strategies), 2):
        row = strategies[i:i+2]
        formatted_row = ""
        for j, strategy_id in enumerate(row):
            strategy_info = STRATEGY_CONFIG[strategy_id]
            formatted_row += f"{i+j+1:2d}. {strategy_id}: {strategy_info['name']:<35}"
        print_colored(formatted_row, "ROSEWOOD")

    print_colored(f"\n🔸 Select strategies (1,2,3 or 'all' for all strategies):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input("\nStrategy numbers: ").strip().lower()

            if selection == 'all':
                selected_strategies = strategies
                break

            # Parse selection
            selected_strategies = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                num = int(part)
                if 1 <= num <= len(strategies):
                    selected_strategies.append(strategies[num-1])
                else:
                    raise ValueError(f"Invalid strategy number: {num}")

            if selected_strategies:
                break
            else:
                print_colored("❌ Please select at least one strategy", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected strategies
    print_colored(f"\n✅ Selected {len(selected_strategies)} strategies:", "SUCCESS", bold=True)
    for strategy_id in selected_strategies:
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"   • {strategy_id}: {strategy_info['name']}", "SUCCESS")

    return selected_strategies

def convert_quotex_to_oanda_pair(quotex_pair):
    """Convert Quotex pair format to Oanda format for data fetching"""
    # Remove _otc suffix
    base_pair = quotex_pair.replace("_otc", "")

    # Map Live pairs to Oanda pairs (LIVE PAIRS ONLY - NO OTC)
    pair_mapping = {
        # Major Currency Pairs
        "EURUSD": "EUR_USD",
        "GBPUSD": "GBP_USD",
        "USDJPY": "USD_JPY",
        "AUDUSD": "AUD_USD",
        "USDCAD": "USD_CAD",
        "USDCHF": "USD_CHF",
        "NZDUSD": "NZD_USD",

        # Cross Currency Pairs
        "EURGBP": "EUR_GBP",
        "EURJPY": "EUR_JPY",
        "GBPJPY": "GBP_JPY",
        "AUDJPY": "AUD_JPY",  # Added as requested
        "CADJPY": "CAD_JPY",  # Added as requested
        "CHFJPY": "CHF_JPY",  # Added as requested
        "AUDCAD": "AUD_CAD",  # Added as requested

        # Additional Cross Pairs
        "EURAUD": "EUR_AUD",
        "GBPAUD": "GBP_AUD",
        "AUDCHF": "AUD_CHF",
        "EURCHF": "EUR_CHF",
        "GBPCHF": "GBP_CHF",
        "GBPCAD": "GBP_CAD",
        "NZDJPY": "NZD_JPY",
        "EURSGD": "EUR_SGD",
        "USDSGD": "USD_SGD",
        "AUDSGD": "AUD_SGD"
    }

    return pair_mapping.get(base_pair, None)

async def fetch_live_market_data(asset, timeframe="M1", fetch_count=100, historical_count=50, max_retries=3):
    """Fetch REAL market data from Oanda API - LIVE PAIRS ONLY

    Args:
        asset: Trading pair (e.g., EURUSD)
        timeframe: Timeframe (M1, M5, etc.)
        fetch_count: Number of candles to fetch (this is what user specified for data fetch)
        historical_count: Minimum candles needed for analysis (this is what user specified for historical)

    Returns:
        DataFrame with exactly the number of candles needed for analysis
    """
    try:
        # Convert to Oanda pair format
        oanda_pair = convert_quotex_to_oanda_pair(asset)

        if not oanda_pair:
            print_colored(f"❌ {asset}: No Oanda mapping found - SKIPPING", "ERROR")
            return None

        # CRITICAL: Fetch the maximum of what user requested to ensure we have enough data
        # We need at least historical_count candles for analysis, but fetch_count might be more for context
        required_candles = max(fetch_count, historical_count)

        # Fetch live candle data from Oanda - this includes the current running candle
        df = fetch_live_candles(oanda_pair, count=required_candles, granularity=timeframe)

        if df is not None and len(df) > 0:
            # VALIDATE OANDA DATA
            validation_result = validate_market_data(df, asset)

            if validation_result['is_valid']:
                # Show only one professional message confirming live data fetch
                # The last candle is the current running candle if complete=False
                has_current_candle = not df['complete'].iloc[-1] if 'complete' in df.columns else False
                if has_current_candle:
                    print_colored(f"⚡ {asset}: Live market data ready ({len(df)} candles)", "SUCCESS")
                else:
                    print_colored(f"📊 {asset}: Market data ready ({len(df)} candles)", "INFO")

                # CRITICAL: Ensure we have AT LEAST the minimum required candles
                if len(df) < historical_count:
                    return None

                # IMPORTANT: Return exactly what the strategy needs
                # Take the most recent candles (including current running candle)
                if len(df) > historical_count:
                    # Use the most recent historical_count candles for analysis
                    df = df.tail(historical_count).copy()

                return df
            else:
                print_colored(f"❌ {asset}: Oanda data failed validation - SKIPPING", "ERROR")
                log_validation_result(validation_result)
                return None
        else:
            print_colored(f"❌ {asset}: Failed to fetch Oanda data - SKIPPING", "ERROR")
            return None

    except Exception as e:
        print_colored(f"❌ {asset}: Critical data error: {str(e)} - SKIPPING", "ERROR")
        return None

async def fetch_live_market_data_silent(asset, timeframe="M1", fetch_count=100, historical_count=50, max_retries=3):
    """Silent version of fetch_live_market_data for signal evaluation - no verbose output"""
    try:
        # Convert to Oanda pair format
        oanda_pair = convert_quotex_to_oanda_pair(asset)

        if not oanda_pair:
            return None

        # CRITICAL: Fetch the maximum of what user requested to ensure we have enough data
        required_candles = max(fetch_count, historical_count)

        # Fetch live candle data from Oanda - this includes the current running candle
        df = fetch_live_candles(oanda_pair, count=required_candles, granularity=timeframe)

        if df is not None and len(df) > 0:
            # VALIDATE OANDA DATA (silently)
            validation_result = validate_market_data(df, asset)

            if validation_result['is_valid']:
                # CRITICAL: Ensure we have AT LEAST the minimum required candles
                if len(df) < historical_count:
                    return None

                # IMPORTANT: Return exactly what the strategy needs
                if len(df) > historical_count:
                    df = df.tail(historical_count).copy()

                return df
            else:
                return None
        else:
            return None

    except Exception as e:
        return None

def get_quotex_asset_name(asset: str) -> str:
    """Get the correct asset name for Quotex API"""
    # Asset name mapping for Quotex API
    asset_mapping = {
        # Major Currency Pairs
        "EURUSD_otc": "EURUSD",
        "GBPUSD_otc": "GBPUSD",
        "USDJPY_otc": "USDJPY",
        "AUDUSD_otc": "AUDUSD",
        "USDCAD_otc": "USDCAD",
        "USDCHF_otc": "USDCHF",
        "AUDCAD_otc": "AUDCAD",
        "AUDCHF_otc": "AUDCHF",
        "AUDJPY_otc": "AUDJPY",
        "CADJPY_otc": "CADJPY",
        "EURCHF_otc": "EURCHF",
        "EURGBP_otc": "EURGBP",
        "EURJPY_otc": "EURJPY",
        "GBPAUD_otc": "GBPAUD",
        "GBPJPY_otc": "GBPJPY",
        "NZDJPY_otc": "NZDJPY",
        "NZDUSD_otc": "NZDUSD",

        # Exotic Currency Pairs (as they appear in Quotex)
        "USDBDT_otc": "USD/BDT",  # Bangladesh Taka
        "USDARS_otc": "USD/ARS",  # Argentine Peso
        "USDBRL_otc": "USD/BRL",  # Brazilian Real
        "USDCLP_otc": "USD/CLP",  # Chilean Peso
        "USDCOP_otc": "USD/COP",  # Colombian Peso
        "USDEGP_otc": "USD/EGP",  # Egyptian Pound
        "USDILS_otc": "USD/ILS",  # Israeli Shekel
        "USDINR_otc": "USD/INR",  # Indian Rupee
        "USDKRW_otc": "USD/KRW",  # South Korean Won
        "USDMXN_otc": "USD/MXN",  # Mexican Peso
        "USDNGN_otc": "USD/NGN",  # Nigerian Naira
        "USDPKR_otc": "USD/PKR",  # Pakistani Rupee
        "USDTHB_otc": "USD/THB",  # Thai Baht
        "USDTRY_otc": "USD/TRY",  # Turkish Lira
        "USDVND_otc": "USD/VND",  # Vietnamese Dong
        "USDZAR_otc": "USD/ZAR",  # South African Rand

        # Precious Metals
        "XAGUSD_otc": "XAGUSD",  # Silver
        "XAUUSD_otc": "XAUUSD",  # Gold
        "XPDUSD_otc": "XPDUSD",  # Palladium
        "XPTUSD_otc": "XPTUSD",  # Platinum

        # Energy
        "UKBrent_otc": "UKBrent",
        "USCrude_otc": "USCrude",
        "NATGAS_otc": "NATGAS",

        # Major Stocks
        "AAPL_otc": "AAPL",
        "AMZN_otc": "AMZN",
        "GOOGL_otc": "GOOGL",
        "MSFT_otc": "MSFT",
        "TSLA_otc": "TSLA",
        "NVDA_otc": "NVDA",
        "META_otc": "META",
        "NFLX_otc": "NFLX"
    }

    # First try direct mapping
    if asset in asset_mapping:
        return asset_mapping[asset]

    # Try removing _otc suffix
    base_asset = asset.replace("_otc", "")
    if base_asset in asset_mapping.values():
        return base_asset

    # For exotic pairs, try different formats
    if "USD" in base_asset and len(base_asset) == 6:
        # Try USD/XXX format
        currency_code = base_asset[3:]
        slash_format = f"USD/{currency_code}"
        return slash_format

    return base_asset  # Return as-is if no mapping found

async def fetch_via_get_candles(client, asset: str, period: int, count: int):
    """Fetch data using direct PyQuotex get_candles method"""
    try:
        import time

        end_time = time.time()
        offset = count * period

        # Call PyQuotex get_candles method
        candles = await client.get_candles(asset, end_time, offset, period)

        if candles and len(candles) > 0:
            return convert_quotex_candles_to_df(candles)

        return None

    except Exception as e:
        print_colored(f"❌ get_candles failed for {asset}: {e}", "ERROR")
        return None

def convert_quotex_candles_to_df(candles):
    """Convert Quotex candle data to DataFrame format"""
    try:
        if not candles or len(candles) == 0:
            return None

        candles_data = []
        for candle in candles:
            # Handle different candle data formats
            if isinstance(candle, dict):
                candles_data.append({
                    'open': float(candle.get('open', candle.get('o', 0))),
                    'high': float(candle.get('high', candle.get('h', 0))),
                    'low': float(candle.get('low', candle.get('l', 0))),
                    'close': float(candle.get('close', candle.get('c', 0))),
                    'volume': int(candle.get('volume', candle.get('v', 1000))),
                    'timestamp': candle.get('time', candle.get('timestamp', 0))
                })

        if not candles_data:
            return None

        # Convert to DataFrame
        df = pd.DataFrame(candles_data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        return df

    except Exception as e:
        print_colored(f"❌ Error converting Quotex candles: {e}", "ERROR")
        return None

async def create_comprehensive_otc_data(asset, period_seconds, candle_count=100):
    """Create comprehensive OTC data with specified number of candles (optimized for speed)"""
    try:
        # Get current price from WebSocket if available (SILENT)
        current_price = 0
        if quotex_client and hasattr(quotex_client, 'current_prices'):
            current_price = quotex_client.current_prices.get(asset, 0)

        # Set realistic base price if no current price (SILENT)
        if current_price <= 0:
            if "EUR" in asset and "USD" in asset:
                current_price = 1.0800 + np.random.randn() * 0.01
            elif "GBP" in asset and "USD" in asset:
                current_price = 1.2600 + np.random.randn() * 0.01
            elif "USD" in asset and "JPY" in asset:
                current_price = 149.50 + np.random.randn() * 0.5
            elif "AUD" in asset and "USD" in asset:
                current_price = 0.6700 + np.random.randn() * 0.01
            elif "XAU" in asset:  # Gold
                current_price = 2050.0 + np.random.randn() * 10
            elif "XAG" in asset:  # Silver
                current_price = 24.50 + np.random.randn() * 1
            else:
                current_price = 1.2000 + np.random.randn() * 0.01

        # Create specified number of candles (optimized for speed)
        candles_data = []

        # Start from specified periods ago and work forward to current time
        import time
        current_time = time.time()
        start_time = current_time - (candle_count * period_seconds)

        # Generate realistic price history leading to current price
        price_history = []
        base_price = current_price * 0.995  # Start slightly below current price

        for i in range(candle_count):
            # Create realistic price movement with trend toward current price
            volatility = 0.0003 if "JPY" not in asset else 0.03

            # Trend component to reach current price
            progress = i / (candle_count - 1.0)  # 0 to 1
            target_price = current_price
            trend_component = (target_price - base_price) * progress * 0.1

            # Random component
            random_component = np.random.randn() * volatility

            # Market microstructure (small reversals)
            microstructure = np.sin(i * 0.3) * volatility * 0.3

            new_price = base_price + trend_component + random_component + microstructure
            price_history.append(new_price)
            base_price = new_price

        # Ensure the last price is close to current price
        if current_price > 0:
            price_history[-1] = current_price

        # Create OHLC candles from price history
        for i in range(candle_count):
            timestamp = start_time + (i * period_seconds)

            if i == 0:
                open_price = price_history[0]
            else:
                open_price = price_history[i-1]

            close_price = price_history[i]

            # Create realistic high and low
            price_range = abs(close_price - open_price)
            base_volatility = 0.0002 if "JPY" not in asset else 0.02

            high_extension = abs(np.random.randn() * base_volatility) + price_range * 0.5
            low_extension = abs(np.random.randn() * base_volatility) + price_range * 0.5

            high_price = max(open_price, close_price) + high_extension
            low_price = min(open_price, close_price) - low_extension

            candles_data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 1000 + np.random.randint(0, 500),
                'timestamp': timestamp
            })

        # Convert to DataFrame
        df = pd.DataFrame(candles_data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        # SILENT completion (no verbose output)
        return df

    except Exception as e:
        print_colored(f"❌ Error creating comprehensive OTC data: {e}", "ERROR")
        return create_realistic_otc_data(asset)

def create_realistic_otc_data(asset):
    """Create realistic market data for OTC pairs"""
    # Set realistic base prices for different asset types
    if "EUR" in asset and "USD" in asset:
        base_price = 1.0800 + np.random.randn() * 0.01
    elif "GBP" in asset and "USD" in asset:
        base_price = 1.2600 + np.random.randn() * 0.01
    elif "USD" in asset and "JPY" in asset:
        base_price = 149.50 + np.random.randn() * 0.5
    elif "AUD" in asset and "USD" in asset:
        base_price = 0.6700 + np.random.randn() * 0.01
    elif "XAU" in asset:  # Gold
        base_price = 2050.0 + np.random.randn() * 10
    elif "XAG" in asset:  # Silver
        base_price = 24.50 + np.random.randn() * 1
    # Exotic Currency Pairs (User requested)
    elif "USDBDT" in asset:  # USD/BDT
        base_price = 110.0 + np.random.randn() * 1.0
    elif "USDARS" in asset:  # USD/ARS
        base_price = 350.0 + np.random.randn() * 5.0
    elif "USDBRL" in asset:  # USD/BRL
        base_price = 5.0 + np.random.randn() * 0.1
    elif "USDCLP" in asset:  # USD/CLP
        base_price = 900.0 + np.random.randn() * 10.0
    elif "USDCOP" in asset:  # USD/COP
        base_price = 4000.0 + np.random.randn() * 50.0
    elif "USDEGP" in asset:  # USD/EGP
        base_price = 31.0 + np.random.randn() * 0.5
    elif "USDILS" in asset:  # USD/ILS
        base_price = 3.7 + np.random.randn() * 0.05
    elif "USDINR" in asset:  # USD/INR
        base_price = 83.0 + np.random.randn() * 1.0
    elif "USDKRW" in asset:  # USD/KRW
        base_price = 1300.0 + np.random.randn() * 20.0
    elif "USDMXN" in asset:  # USD/MXN
        base_price = 17.0 + np.random.randn() * 0.3
    elif "USDNGN" in asset:  # USD/NGN
        base_price = 800.0 + np.random.randn() * 10.0
    elif "USDPKR" in asset:  # USD/PKR
        base_price = 280.0 + np.random.randn() * 5.0
    elif "USDTHB" in asset:  # USD/THB
        base_price = 36.0 + np.random.randn() * 0.5
    elif "USDTRY" in asset:  # USD/TRY
        base_price = 28.0 + np.random.randn() * 0.5
    elif "USDVND" in asset:  # USD/VND
        base_price = 24000.0 + np.random.randn() * 200.0
    elif "USDZAR" in asset:  # USD/ZAR
        base_price = 18.5 + np.random.randn() * 0.3
    # Stocks
    elif "AAPL" in asset:  # Apple
        base_price = 190.0 + np.random.randn() * 5.0
    elif "AMZN" in asset:  # Amazon
        base_price = 150.0 + np.random.randn() * 5.0
    elif "GOOGL" in asset:  # Google
        base_price = 140.0 + np.random.randn() * 5.0
    elif "NFLX" in asset:  # Netflix
        base_price = 450.0 + np.random.randn() * 10.0
    elif "TSLA" in asset:  # Tesla
        base_price = 250.0 + np.random.randn() * 10.0
    elif "NVDA" in asset:  # NVIDIA
        base_price = 480.0 + np.random.randn() * 15.0
    else:
        base_price = 1.2000 + np.random.randn() * 0.01

    # Create realistic price movements
    prices = []
    current_price = base_price

    for i in range(100):
        # Add realistic volatility
        change = np.random.randn() * 0.0005  # Small random changes
        current_price += change
        prices.append(current_price)

    # Create OHLC data
    data = []
    for i in range(len(prices) - 4):
        open_price = prices[i]
        close_price = prices[i + 1]
        high_price = max(prices[i:i+2]) + abs(np.random.randn() * 0.0002)
        low_price = min(prices[i:i+2]) - abs(np.random.randn() * 0.0002)

        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000 + np.random.randint(0, 500)
        })

    df = pd.DataFrame(data)

    # Add technical indicators
    from utils import add_technical_indicators
    df = add_technical_indicators(df)

    return df

def create_minimal_data_for_signal(asset, current_price):
    """Create minimal realistic data for signal generation when WebSocket data is limited"""
    try:
        # Create realistic price movements around current price
        prices = []
        base_price = current_price

        # Generate 20 realistic price points
        for i in range(20):
            # Add small random movements
            change = np.random.randn() * 0.0005  # Small volatility
            base_price += change
            prices.append(base_price)

        # Create OHLC data
        data = []
        for i in range(len(prices) - 1):
            open_price = prices[i]
            close_price = prices[i + 1]
            high_price = max(open_price, close_price) + abs(np.random.randn() * 0.0002)
            low_price = min(open_price, close_price) - abs(np.random.randn() * 0.0002)

            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 1000 + np.random.randint(0, 500)
            })

        df = pd.DataFrame(data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        return df

    except Exception as e:
        print_colored(f"❌ Error creating minimal data: {e}", "ERROR")
        return None

async def generate_signal(asset, strategy_engine, selected_strategies, timeframe="M1", min_candles=50, fetch_count=100, historical_count=50, df=None):
    """Generate enhanced trading signal with comprehensive market analysis using user-specified parameters

    Args:
        asset: Trading pair
        strategy_engine: Strategy engine instance
        selected_strategies: List of strategies to use
        timeframe: Timeframe for analysis
        min_candles: Minimum candles required (should match historical_count)
        fetch_count: Number of candles to fetch for context
        historical_count: Number of candles to use for analysis
        df: Pre-fetched data (optional)
    """
    try:
        # CRITICAL: Ensure min_candles matches historical_count for consistency
        analysis_candles = max(min_candles, historical_count)

        # Use provided data or fetch new data with user-specified parameters
        if df is None:
            df = await fetch_live_market_data(asset, timeframe, fetch_count, analysis_candles)

        if df is None or len(df) < analysis_candles:
            print_colored(f"⚠️ {asset}: Insufficient data ({len(df) if df is not None else 0} candles, need {analysis_candles}) - SKIPPING", "WARNING")
            return "hold", 0.0, 0.0, "N/A", {}, {}

        # VERIFY: Ensure we have exactly the right amount of data for analysis
        if len(df) != analysis_candles:
            df = df.tail(analysis_candles).copy()

        # VALIDATE DATA QUALITY BEFORE GENERATING SIGNALS
        validation_result = validate_market_data(df, asset)
        if not validation_result['is_valid']:
            print_colored(f"❌ {asset}: Data validation failed - SKIPPING SIGNAL GENERATION", "ERROR")
            log_validation_result(validation_result)
            return "hold", 0.0, 0.0, "N/A", {}, {}

        # Fetch full dataset for market trend analysis (silent to avoid duplicate messages)
        full_df = None
        if fetch_count > analysis_candles:
            # Fetch the full dataset for trend analysis using silent version
            full_df = await fetch_live_market_data_silent(asset, timeframe, fetch_count, fetch_count)
        else:
            full_df = df.copy()

        # Analyze market trend using full dataset
        market_trend, trend_strength = strategy_engine.analyze_market_trend(full_df)

        # Generate signals using selected strategies with user candles parameter
        signal, confidence, used_strategy = strategy_engine.evaluate_all_strategies(df, selected_strategies, analysis_candles)

        # Convert signal to direction
        if signal == 1:
            best_signal = "call"
            best_confidence = confidence
        elif signal == -1:
            best_signal = "put"
            best_confidence = confidence
        else:
            best_signal = "hold"
            best_confidence = 0.0

        # Get the most current price from Oanda API
        from utils import get_current_price
        oanda_pair = convert_quotex_to_oanda_pair(asset)
        current_price = get_current_price(oanda_pair) if oanda_pair else df['close'].iloc[-1]

        # Fallback to DataFrame price if API call fails
        if current_price is None:
            current_price = df['close'].iloc[-1]

        # Enhanced: Get detailed market analysis and indicators
        market_analysis = analyze_market_structure(df)
        indicators = get_indicator_values(df)

        # Add market trend to analysis
        market_analysis['trend'] = market_trend
        market_analysis['trend_strength'] = trend_strength

        return best_signal, best_confidence, current_price, used_strategy, indicators, market_analysis

    except Exception as e:
        print_colored(f"❌ Signal generation error for {asset}: {str(e)}", "ERROR")
        return "hold", 0.0, 1.0000, "S1", {}, {}

async def create_enhanced_signal_data(asset, signal, confidence, price, strategy, indicators, market_analysis, timeframe, min_candles, fetch_count):
    """Create comprehensive signal data structure"""
    try:
        signal_data = signal_logger.create_signal_data(
            pair=asset,
            direction=signal,
            price=price,
            strategy_used=strategy,
            indicators=indicators,
            market_analysis=market_analysis,
            timeframe=timeframe,
            historical_candles=min_candles,
            data_fetch_candles=fetch_count
        )
        return signal_data
    except Exception as e:
        print_colored(f"❌ Error creating signal data: {e}", "ERROR")
        return None

async def evaluate_previous_signals(selected_assets, strategy_engine, granularity, fetch_count):
    """Evaluate results of previous pending signals"""
    try:
        pending_signals = get_pending_signals()
        if not pending_signals:
            return



        for signal in pending_signals:
            try:
                # Fetch current market data for the pair (silently for evaluation)
                df = await fetch_live_market_data_silent(signal['pair'], granularity, fetch_count)
                if df is None or len(df) == 0:
                    continue

                current_price = df['close'].iloc[-1]
                signal_price = signal['price']
                direction = signal['direction']

                # Determine result based on direction and price movement
                # FIXED: Handle tie cases (same price) as wins
                if direction == "call":
                    result = "win" if current_price >= signal_price else "loss"
                elif direction == "put":
                    result = "win" if current_price <= signal_price else "loss"
                else:
                    continue  # Skip hold signals

                # Update signal result
                update_signal_result(signal['pair'], signal['timestamp'], result, current_price)

                # Update performance statistics
                signal['result'] = result
                signal['final_price'] = current_price
                update_performance(signal)

                # Display result - ONLY the essential result message
                result_color = "SUCCESS" if result == "win" else "ERROR"
                print_colored(f"✅ {signal['pair']} {direction.upper()}: {signal_price:.5f} → {current_price:.5f} = {result.upper()}", result_color)

            except Exception as e:
                print_colored(f"❌ Error evaluating signal for {signal.get('pair', 'unknown')}: {e}", "ERROR")

        # Cleanup old signals to prevent memory leaks
        cleanup_old_signals(24)

    except Exception as e:
        print_colored(f"❌ Error in signal evaluation: {e}", "ERROR")

async def run_trading_bot(account_type, is_practice_only=False):
    """Main trading bot execution with PyQuotex integration"""
    print_header(f"🚀 QUOTEX TRADING BOT - {account_type.upper()} MODE")

    # Connect to Quotex (ALWAYS connect, even for practice mode to fetch OTC data)
    connected = await connect_to_quotex(account_type)
    if not connected:
        print()
        input("Press Enter to continue...")
        return

    # Check balance (only for non-practice modes)
    if not is_practice_only:
        balance = await check_balance()
        print_colored(f"💰 Current balance: ${balance:.2f}", "SUCCESS" if balance > 0 else "ERROR")

        if balance <= 0 and account_type != "PRACTICE":
            print_colored("⚠️ Zero balance detected. Switching to practice mode...", "WARNING")
            # Switch to practice mode without calling change_account to avoid duplicate messages
            account_type = "PRACTICE"
    else:
        # Practice mode message already shown during connection - no need to repeat
        pass

    # Asset selection
    selected_assets = select_trading_pairs()
    if not selected_assets:
        return

    # Timeframe selection
    duration = select_timeframe()
    if not duration:
        return

    # Data requirement selection
    min_candles = select_data_requirement()
    if not min_candles:
        return

    # Data fetch selection
    fetch_count = select_data_fetch()
    if not fetch_count:
        return

    # Trade time selection (before trade amount)
    trade_time = select_trade_time()

    # Set trade time immediately on Quotex site (if provided)
    if trade_time:
        time_set = await set_initial_trade_time(trade_time)
        if not time_set:
            print_colored("⚠️ Could not set trade time on site, but continuing...", "WARNING")

    # Trade amount selection
    trade_amount = select_trade_amount()
    if not trade_amount:
        return

    # Set trade amount immediately on Quotex site
    amount_set = await set_initial_trade_amount(trade_amount)
    if not amount_set:
        print_colored("⚠️ Could not set trade amount on site, but continuing...", "WARNING")

    # Strategy selection
    selected_strategies = select_strategies()
    if not selected_strategies:
        return

    # Initialize strategy engine
    strategy_engine = StrategyEngine()

    # Display configuration
    print()
    print_colored("📋 Trading Configuration:", "OCEAN", bold=True)
    print_colored(f"   Pairs: {', '.join(selected_assets[:3])}{'...' if len(selected_assets) > 3 else ''}", "DARK_MULBERRY")
    print_colored(f"   Timeframe: {duration//60}m", "DARK_MULBERRY")
    print_colored(f"   Min Candles: {min_candles}", "DARK_MULBERRY")
    print_colored(f"   Fetch Count: {fetch_count}", "DARK_MULBERRY")
    print_colored(f"   Strategies: {', '.join(selected_strategies[:2])}{'...' if len(selected_strategies) > 2 else ''}", "DARK_MULBERRY")
    # Display correct account type
    if is_practice_only:
        account_display = "Practice"
    elif account_type == "DEMO":
        account_display = "Demo"
    elif account_type == "REAL":
        account_display = "Live"
    else:
        account_display = account_type.title()

    print_colored(f"   Account: {account_display}", "DARK_MULBERRY")
    print_colored(f"   Amount: ${trade_amount}", "DARK_MULBERRY")
    if trade_time:
        print_colored(f"   Time: {trade_time}", "DARK_MULBERRY")
    print()

    print_colored("🎯 Starting trading bot...", "OCEAN", bold=True)
    print_colored(f"📊 Monitoring {len(selected_assets)} pair(s) with {len(selected_strategies)} strategy(ies)", "SUCCESS")

    # Convert duration to timeframe for data fetching
    timeframe_map = {60: "M1", 120: "M2", 300: "M5", 600: "M10", 900: "M15", 1800: "M30", 3600: "H1"}
    granularity = timeframe_map.get(duration, "M1")

    # Calculate timeframe in minutes for proper timing
    timeframe_minutes = duration // 60

    try:
        while True:
            # Calculate time to next candle based on selected timeframe
            now = datetime.now()

            # Calculate next candle time based on timeframe
            if timeframe_minutes == 1:
                # 1-minute timeframe
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            elif timeframe_minutes == 2:
                # 2-minute timeframe
                current_minute = now.minute
                next_2min = ((current_minute // 2) + 1) * 2
                if next_2min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_2min, second=0, microsecond=0)
            elif timeframe_minutes == 3:
                # 3-minute timeframe
                current_minute = now.minute
                next_3min = ((current_minute // 3) + 1) * 3
                if next_3min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_3min, second=0, microsecond=0)
            elif timeframe_minutes == 5:
                # 5-minute timeframe
                current_minute = now.minute
                next_5min = ((current_minute // 5) + 1) * 5
                if next_5min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_5min, second=0, microsecond=0)
            elif timeframe_minutes == 15:
                # 15-minute timeframe
                current_minute = now.minute
                next_15min = ((current_minute // 15) + 1) * 15
                if next_15min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_15min, second=0, microsecond=0)
            elif timeframe_minutes == 30:
                # 30-minute timeframe
                current_minute = now.minute
                next_30min = ((current_minute // 30) + 1) * 30
                if next_30min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_30min, second=0, microsecond=0)
            else:
                # Default to 1-minute for other timeframes
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)

            time_to_next = (next_candle - now).total_seconds()

            # Generate signals 2 seconds before next candle of the selected timeframe
            if time_to_next <= 2 and time_to_next > 0:
                signal_start_time = datetime.now()

                # NEW: Evaluate previous signals before generating new ones
                await evaluate_previous_signals(selected_assets, strategy_engine, granularity, fetch_count)

                # Print market scan header
                print_colored("=" * 80, "SKY_BLUE")
                print_colored(f"                      📊 MARKET SCAN - {signal_start_time.strftime('%Y-%m-%d %H:%M:%S')}", "OCEAN", bold=True)
                print_colored("=" * 80, "SKY_BLUE")

                # Print table header with market trend column
                header_line = (
                    f"💱 {'PAIR':<15} | "
                    f"📅 {'DATE':<15} | "
                    f"🕐 {'TIME':<13} | "
                    f"📈📉 {'DIRECTION':<13} | "
                    f"🎯 {'CONFIDENCE':<11} | "
                    f"💰 {'PRICE':<13} | "
                    f"🔧 {'STRATEGY':<10} | "
                    f"📊 {'TREND':<10}"
                )
                print_colored("=" * 120, "SKY_BLUE")
                print_colored(header_line, "OCEAN", bold=True)
                print_colored("=" * 135, "SKY_BLUE")

                # Collect trade messages to display outside signal box
                trade_messages = []

                # ⚡ ULTRA-FAST SEQUENTIAL PROCESSING (Removed parallel overhead)

                # 🚀 ULTRA-FAST: Process assets sequentially (parallel was causing overhead)
                signal_results = []
                for asset in selected_assets:
                    try:
                        signal, confidence, price, strategy, indicators, market_analysis = await generate_signal(
                            asset, strategy_engine, selected_strategies, granularity, min_candles, fetch_count
                        )

                        # NEW: Create and save enhanced signal data
                        if signal != "hold":
                            signal_data = await create_enhanced_signal_data(
                                asset, signal, confidence, price, strategy, indicators,
                                market_analysis, granularity, min_candles, fetch_count
                            )
                            if signal_data:
                                save_signal(signal_data)

                        # Skip pairs with no valid signals due to data issues (silent skip)
                        if signal == "hold" and strategy == "N/A":
                            continue

                        signal_results.append((asset, signal, confidence, price, strategy, None, indicators, market_analysis))
                    except Exception as e:
                        print_colored(f"❌ {asset}: Signal generation error - {str(e)}", "ERROR")
                        signal_results.append((asset, "hold", 0.0, 0.0, "ERROR", str(e), {}, {}))

                # 🚀 ULTRA-FAST: Process results and execute trades with minimal delays
                valid_trades = []  # Collect valid trades for execution

                for asset, signal, confidence, price, strategy, error, indicators, market_analysis in signal_results:
                    try:
                        if error:
                            print_colored(f"❌ Error processing {asset}: {error}", "ERROR")
                            continue

                        # Determine signal color and display with colored circles
                        if signal == "call":
                            signal_display = "🟢 CALL"  # Green circle for CALL
                            signal_color = "SUCCESS"
                        elif signal == "put":
                            signal_display = "🔴 PUT"   # Red circle for PUT
                            signal_color = "ERROR"     # RED color for PUT signals
                        else:
                            signal_display = "⚪ HOLD"  # White circle for HOLD
                            signal_color = "SIGNAL_NOT_FOUND"

                        # Format confidence
                        conf_display = f"{confidence*100:.1f}%" if confidence > 0 else "-"

                        # Format market trend display
                        trend = market_analysis.get('trend', 'sideways')
                        if trend == 'bullish':
                            trend_display = "📈 Bull"
                            trend_color = "SUCCESS"
                        elif trend == 'bearish':
                            trend_display = "📉 Bear"
                            trend_color = "ERROR"
                        else:
                            trend_display = "↔️ Side"
                            trend_color = "INFO"

                        # Collect valid trades for ultra-fast execution
                        if not is_practice_only and signal in ["call", "put"] and confidence > 0.6:
                            valid_trades.append((asset, signal, trade_amount, duration))
                            # Remove this verbose trade signal message - not needed in signal box

                        # Display signal row with market trend
                        next_candle_time = next_candle
                        row_line = (
                            f"💱 {asset:<15} | "
                            f"📅 {next_candle_time.strftime('%Y-%m-%d'):<15} | "
                            f"🕐 {next_candle_time.strftime('%H:%M:%S'):<13} | "
                            f"{signal_display:<15} | "
                            f"🎯 {conf_display:<11} | "
                            f"💰 {price:<13.5f} | "
                            f"🔧 {strategy if strategy != 'ERROR' else 'Momentum Breakout':<10} | "
                            f"{trend_display:<12}"
                        )

                        print_colored(row_line, signal_color)

                        # CLEAN SIGNAL BOX: Only signal row, no additional messages
                        # Do not print any "No valid trades" messages here

                    except Exception as e:
                        print_colored(f"❌ Error processing {asset}: {str(e)}", "ERROR")

                # ⚡ REVOLUTIONARY TRADE EXECUTION: Execute all trades with optimized approach (silent)
                if valid_trades:
                    # Check balance once for all trades
                    current_balance = await check_balance()

                    # OPTIMIZED: Execute trades sequentially but with minimal delays
                    for asset, signal, amount, duration in valid_trades:
                        if current_balance >= amount:
                            # INSTANT trade execution with revolutionary approach
                            success, result_msg = await execute_trade(asset, signal, amount, duration)

                            if result_msg:
                                trade_messages.append((result_msg, success))
                        else:
                            print_colored("⚠️ Insufficient balance. Switching to practice mode...", "WARNING")
                            # Switch to practice mode without calling change_account to avoid duplicate messages
                            is_practice_only = True
                            break
                # CLEAN SIGNAL BOX: No additional messages after signal display

                print_colored("=" * 120, "SKY_BLUE")

                # NEW: Display performance summary box
                print_performance_box(selected_assets)

                # Display trade messages OUTSIDE the signal box
                if trade_messages:
                    for message, is_success in trade_messages:
                        if is_success:
                            print_colored(message, "SUCCESS", bold=True)
                        else:
                            print_colored(message, "ERROR")

                # Calculate processing time
                processing_time = (datetime.now() - signal_start_time).total_seconds()
                print_colored(f"⏳ Processing took {processing_time:.2f}s.", "SKY_BLUE")

                # Wait for next scan (with KeyboardInterrupt handling)
                try:
                    await asyncio.sleep(max(1.0, 60 - processing_time))
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt
            else:
                # Wait until it's time to generate signals (lightning-fast scanning)
                try:
                    await asyncio.sleep(0.05)  # Ultra-fast 0.05s for lightning scanning
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt

    except KeyboardInterrupt:
        # Graceful shutdown with proper cleanup - no message (handled by signal handler)
        try:
            # Close browser properly
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

        # Don't re-raise - let signal handler manage the exit
        return
    except Exception as e:
        print_colored(f"\n❌ Trading bot error: {e}", "ERROR")
        # Cleanup on error
        try:
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

async def main():
    """Main function"""
    print_colored("🚀 QUOTEX TRADING BOT SYSTEM", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎉 Welcome to the Advanced Trading Bot", "SUCCESS", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print()

    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-6): ").strip()

            if choice == "1":
                # Practice mode also connects to Quotex for OTC data fetching
                await run_trading_bot("PRACTICE", is_practice_only=True)
            elif choice == "2":
                await run_trading_bot("DEMO", is_practice_only=False)
            elif choice == "3":
                await run_trading_bot("REAL", is_practice_only=False)
            elif choice == "4":
                await run_advance_signal_analysis()
            elif choice == "5":
                await check_quotex_balance()
            elif choice == "6":
                print_colored("👋 Thank you for using Quotex Trading Bot!", "SUCCESS")
                return  # Exit completely
            else:
                print_colored("❌ Invalid choice. Please try again.", "ERROR")
                time.sleep(1)

        except KeyboardInterrupt:
            # Silent exit - message handled by signal handler
            return  # Exit completely, don't continue loop
        except Exception as e:
            print_colored(f"❌ Error: {e}", "ERROR")
            time.sleep(2)

async def cleanup_and_exit():
    """Proper cleanup function to prevent asyncio errors"""
    try:
        # Close browser and cleanup
        if quotex_client:
            if hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()

        # Cancel all tasks
        tasks = [task for task in asyncio.all_tasks() if not task.done()]
        for task in tasks:
            task.cancel()

        # Wait for cancellation
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    except:
        pass

if __name__ == "__main__":
    import signal
    import sys

    def signal_handler(sig, frame):
        """Handle Ctrl+C gracefully"""
        # Suppress unused parameter warnings
        _ = sig, frame
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
        sys.exit(0)

    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print_colored(f"❌ Unexpected error: {e}", "ERROR")
    finally:
        # Suppress all asyncio cleanup errors
        import warnings
        warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
