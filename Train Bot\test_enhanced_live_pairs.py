#!/usr/bin/env python3
"""
Test script for enhanced live pairs system
Tests all new pairs with user-specified parameters and strategy analysis
"""

import sys
import os
import asyncio
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils import fetch_live_candles, fetch_historical_candles, get_oanda_headers, print_colored
    from Model import convert_quotex_to_oanda_pair, fetch_live_market_data, generate_signal
    from strategy_engine import StrategyEngine
    from data_validator import validate_market_data, log_validation_result
    from config import OANDA_CONFIG
    import requests
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Enhanced live pairs list (NO OTC PAIRS)
LIVE_PAIRS = [
    # Major Currency Pairs
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD",
    
    # Cross Currency Pairs (including new ones)
    "EURGBP", "EURJPY", "GBPJPY", 
    "AUDJPY",  # New
    "CADJPY",  # New
    "CHFJPY",  # New
    "AUDCAD",  # New
    
    # Additional Cross Pairs
    "EURAUD", "GBPAUD", "AUDCHF", "EURCHF", "GBPCHF", "GBPCAD", "NZDJPY"
]

# Test parameters (as user would provide)
TEST_PARAMETERS = {
    "historical_candles": 50,  # User-specified historical candles
    "fetch_candles": 100,       # User-specified fetch candles
    "strategy": "S1",           # User-selected strategy
    "timeframe": "M1"           # User-selected timeframe
}

async def test_oanda_connection():
    """Test Oanda API connection"""
    print_colored("\n🧪 Testing Oanda API Connection...", "INFO", bold=True)
    
    try:
        headers = get_oanda_headers()
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}"
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            account_data = response.json()
            account_info = account_data.get('account', {})
            
            print_colored("✅ Oanda API connection successful!", "SUCCESS")
            print_colored(f"   Account ID: {account_info.get('id', 'Unknown')}", "INFO")
            print_colored(f"   Currency: {account_info.get('currency', 'Unknown')}", "INFO")
            print_colored(f"   Balance: {account_info.get('balance', 'Unknown')}", "INFO")
            
            return True
        else:
            print_colored(f"❌ Oanda API connection failed: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ Oanda connection error: {e}", "ERROR")
        return False

async def test_enhanced_data_fetching(pair):
    """Test enhanced data fetching with user parameters"""
    print_colored(f"\n📊 Testing {pair} with enhanced parameters...", "INFO")
    
    try:
        # Convert to Oanda format
        oanda_pair = convert_quotex_to_oanda_pair(pair)
        
        if not oanda_pair:
            print_colored(f"❌ {pair}: No Oanda mapping found", "ERROR")
            return False
        
        print_colored(f"   Oanda pair: {oanda_pair}", "INFO")
        print_colored(f"   Parameters: Historical={TEST_PARAMETERS['historical_candles']}, Fetch={TEST_PARAMETERS['fetch_candles']}", "INFO")
        
        # Test enhanced data fetching
        df = await fetch_live_market_data(
            pair, 
            TEST_PARAMETERS['timeframe'], 
            TEST_PARAMETERS['fetch_candles'], 
            TEST_PARAMETERS['historical_candles']
        )
        
        if df is None or len(df) == 0:
            print_colored(f"❌ {pair}: No data received", "ERROR")
            return False
        
        print_colored(f"✅ {pair}: Got {len(df)} candles", "SUCCESS")
        
        # Validate data quality
        validation_result = validate_market_data(df, pair)
        
        if not validation_result['is_valid']:
            print_colored(f"❌ {pair}: Data validation failed", "ERROR")
            return False
        
        print_colored(f"✅ {pair}: Data validation passed", "SUCCESS")
        
        # Check if we have enough data for analysis
        if len(df) >= TEST_PARAMETERS['historical_candles']:
            print_colored(f"✅ {pair}: Sufficient data for analysis ({len(df)}>={TEST_PARAMETERS['historical_candles']})", "SUCCESS")
        else:
            print_colored(f"⚠️ {pair}: Limited data ({len(df)}<{TEST_PARAMETERS['historical_candles']})", "WARNING")
        
        # Show sample data
        latest = df.iloc[-1]
        print_colored(f"   Latest: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}", "INFO")
        print_colored(f"   Volume: {latest['volume']:.0f} | Timestamp: {latest['timestamp']:.0f}", "INFO")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ {pair}: Error - {e}", "ERROR")
        return False

async def test_strategy_with_parameters(pair):
    """Test strategy analysis with user-specified parameters"""
    print_colored(f"\n🧠 Testing Strategy Analysis for {pair}...", "INFO")
    
    try:
        # Initialize strategy engine
        strategy_engine = StrategyEngine()
        
        print_colored(f"🎯 Using Strategy: {TEST_PARAMETERS['strategy']}", "INFO")
        print_colored(f"📊 Historical candles: {TEST_PARAMETERS['historical_candles']}", "INFO")
        print_colored(f"📈 Fetch candles: {TEST_PARAMETERS['fetch_candles']}", "INFO")
        
        # Generate signal using user parameters
        signal, confidence, price, used_strategy, indicators, market_analysis = await generate_signal(
            pair, 
            strategy_engine, 
            [TEST_PARAMETERS['strategy']], 
            TEST_PARAMETERS['timeframe'], 
            TEST_PARAMETERS['historical_candles'],  # min_candles = historical_candles
            TEST_PARAMETERS['fetch_candles'],       # fetch_count
            TEST_PARAMETERS['historical_candles']   # historical_count
        )
        
        if signal == "hold" and confidence == 0.0:
            print_colored(f"⚠️ {pair}: No signal generated (HOLD)", "WARNING")
            return False
        
        print_colored(f"✅ {pair}: Signal generated successfully!", "SUCCESS")
        print_colored(f"   Signal: {signal.upper()}", "SUCCESS")
        print_colored(f"   Confidence: {confidence:.1%}", "SUCCESS")
        print_colored(f"   Price: ${price:.5f}", "INFO")
        print_colored(f"   Strategy Used: {used_strategy}", "INFO")
        
        # Show technical indicators
        if indicators:
            print_colored(f"   RSI: {indicators.get('rsi', 'N/A'):.1f}", "INFO")
            print_colored(f"   MACD: {indicators.get('macd', 'N/A'):.5f}", "INFO")
            print_colored(f"   EMA12: {indicators.get('ema_12', 'N/A'):.5f}", "INFO")
            print_colored(f"   EMA26: {indicators.get('ema_26', 'N/A'):.5f}", "INFO")
        
        # Show market analysis
        if market_analysis:
            print_colored(f"   Market Trend: {market_analysis.get('market_trend', 'N/A')}", "INFO")
            print_colored(f"   Support: ${market_analysis.get('support_level', 'N/A'):.5f}", "INFO")
            print_colored(f"   Resistance: ${market_analysis.get('resistance_level', 'N/A'):.5f}", "INFO")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ {pair}: Strategy analysis error - {e}", "ERROR")
        return False

async def explain_bot_usage():
    """Explain how the bot uses the provided parameters"""
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📚 HOW THE BOT USES YOUR PARAMETERS", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    print_colored(f"\n🎯 Example: Historical={TEST_PARAMETERS['historical_candles']}, Fetch={TEST_PARAMETERS['fetch_candles']}, Strategy={TEST_PARAMETERS['strategy']}", "INFO", bold=True)
    
    print_colored(f"\n📊 HISTORICAL CANDLES ({TEST_PARAMETERS['historical_candles']}):", "INFO", bold=True)
    print_colored(f"   • Used as MINIMUM data requirement for analysis", "INFO")
    print_colored(f"   • Bot ensures it has AT LEAST {TEST_PARAMETERS['historical_candles']} candles before generating signals", "INFO")
    print_colored(f"   • If live data < {TEST_PARAMETERS['historical_candles']}, bot fetches additional historical data", "INFO")
    print_colored(f"   • Technical indicators (RSI, MACD, EMA) calculated on this data", "INFO")
    print_colored(f"   • More historical data = more accurate technical analysis", "INFO")
    
    print_colored(f"\n📈 FETCH CANDLES ({TEST_PARAMETERS['fetch_candles']}):", "INFO", bold=True)
    print_colored(f"   • Number of recent candles fetched from Oanda API", "INFO")
    print_colored(f"   • Bot requests {TEST_PARAMETERS['fetch_candles']} most recent 1-minute candles", "INFO")
    print_colored(f"   • Higher number = more recent market context", "INFO")
    print_colored(f"   • Used for real-time price action analysis", "INFO")
    print_colored(f"   • Ensures signals are based on latest market movements", "INFO")
    
    print_colored(f"\n🎯 STRATEGY ({TEST_PARAMETERS['strategy']}):", "INFO", bold=True)
    print_colored(f"   • Bot uses ONLY the selected strategy for signal generation", "INFO")
    print_colored(f"   • Strategy {TEST_PARAMETERS['strategy']} analyzes the {TEST_PARAMETERS['historical_candles']} candles", "INFO")
    print_colored(f"   • Applies specific technical rules and conditions", "INFO")
    print_colored(f"   • Generates CALL/PUT signal with confidence percentage", "INFO")
    print_colored(f"   • Signal is based on strategy's analysis of real market data", "INFO")
    
    print_colored(f"\n🔄 COMPLETE PROCESS:", "INFO", bold=True)
    print_colored(f"   1. Fetch {TEST_PARAMETERS['fetch_candles']} recent candles from Oanda", "SUCCESS")
    print_colored(f"   2. Ensure minimum {TEST_PARAMETERS['historical_candles']} candles available", "SUCCESS")
    print_colored(f"   3. Calculate technical indicators on historical data", "SUCCESS")
    print_colored(f"   4. Apply Strategy {TEST_PARAMETERS['strategy']} analysis rules", "SUCCESS")
    print_colored(f"   5. Generate CALL/PUT signal with confidence level", "SUCCESS")
    print_colored(f"   6. Provide real-time trading recommendation", "SUCCESS")
    
    print_colored(f"\n💰 FOR REAL MONEY TRADING:", "WARNING", bold=True)
    print_colored(f"   • Bot uses ONLY real Oanda market data", "WARNING")
    print_colored(f"   • NO fake data or simulated prices", "WARNING")
    print_colored(f"   • Signals based on authentic technical analysis", "WARNING")
    print_colored(f"   • Your parameters directly control analysis depth", "WARNING")
    print_colored(f"   • Higher historical count = more reliable signals", "WARNING")
    
    print_colored("=" * 80, "SKY_BLUE")

async def main():
    """Run comprehensive enhanced live pairs test"""
    print_colored("🚀 ENHANCED LIVE PAIRS SYSTEM TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 Testing live pairs with user-specified parameters", "INFO", bold=True)
    print_colored("🚫 NO OTC PAIRS - LIVE PAIRS ONLY", "WARNING", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Show test parameters
    print_colored(f"\n📋 TEST PARAMETERS:", "INFO", bold=True)
    print_colored(f"   Historical Candles: {TEST_PARAMETERS['historical_candles']}", "INFO")
    print_colored(f"   Fetch Candles: {TEST_PARAMETERS['fetch_candles']}", "INFO")
    print_colored(f"   Strategy: {TEST_PARAMETERS['strategy']}", "INFO")
    print_colored(f"   Timeframe: {TEST_PARAMETERS['timeframe']}", "INFO")
    
    # Test Oanda connection
    oanda_connected = await test_oanda_connection()
    
    if not oanda_connected:
        print_colored("\n❌ OANDA CONNECTION FAILED - Cannot proceed", "ERROR", bold=True)
        return
    
    print_colored(f"\n🧪 Testing Enhanced Data Fetching for {len(LIVE_PAIRS)} Live Pairs...", "INFO", bold=True)
    
    data_results = {}
    # Test first 5 pairs for time efficiency
    for pair in LIVE_PAIRS[:5]:
        success = await test_enhanced_data_fetching(pair)
        data_results[pair] = success
    
    print_colored(f"\n🧪 Testing Strategy Analysis with User Parameters...", "INFO", bold=True)
    
    strategy_results = {}
    # Test strategy analysis for successful pairs
    for pair in LIVE_PAIRS[:3]:
        if data_results.get(pair, False):
            success = await test_strategy_with_parameters(pair)
            strategy_results[pair] = success
        else:
            strategy_results[pair] = False
    
    # Explain how bot uses parameters
    await explain_bot_usage()
    
    # Summary
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📊 ENHANCED LIVE PAIRS TEST RESULTS", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Data fetching results
    data_success = sum(1 for success in data_results.values() if success)
    data_total = len(data_results)
    
    print_colored(f"\n📊 Data Fetching Results:", "INFO", bold=True)
    print_colored(f"   Total tested: {data_total}", "INFO")
    print_colored(f"   ✅ Successful: {data_success}", "SUCCESS" if data_success > 0 else "ERROR")
    print_colored(f"   ❌ Failed: {data_total - data_success}", "ERROR" if data_total - data_success > 0 else "SUCCESS")
    
    # Strategy analysis results
    strategy_success = sum(1 for success in strategy_results.values() if success)
    strategy_total = len(strategy_results)
    
    if strategy_total > 0:
        print_colored(f"\n🧠 Strategy Analysis Results:", "INFO", bold=True)
        print_colored(f"   Total tested: {strategy_total}", "INFO")
        print_colored(f"   ✅ Successful: {strategy_success}", "SUCCESS" if strategy_success > 0 else "ERROR")
        print_colored(f"   ❌ Failed: {strategy_total - strategy_success}", "ERROR" if strategy_total - strategy_success > 0 else "SUCCESS")
    
    # Final verdict
    overall_success_rate = (data_success + strategy_success) / (data_total + strategy_total) if (data_total + strategy_total) > 0 else 0
    
    if overall_success_rate >= 0.8:
        print_colored(f"\n🎉 EXCELLENT: Enhanced live pairs system working perfectly! ({overall_success_rate:.0%})", "SUCCESS", bold=True)
        print_colored("✅ User parameters correctly implemented!", "SUCCESS", bold=True)
        print_colored("✅ Real Oanda data with custom fetch counts!", "SUCCESS", bold=True)
        print_colored("✅ Strategy analysis using historical data!", "SUCCESS", bold=True)
        print_colored("💰 READY FOR REAL MONEY TRADING!", "SUCCESS", bold=True)
    else:
        print_colored(f"⚠️ ISSUES: System needs attention ({overall_success_rate:.0%})", "WARNING", bold=True)
    
    print_colored("=" * 80, "SKY_BLUE")

if __name__ == "__main__":
    asyncio.run(main())
