#!/usr/bin/env python3
"""
Test script to verify the Quotex asset mapper works correctly
Tests loading real asset names and mapping them to bot names
"""

import sys
import os
import asyncio
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from quotex_integration import get_quotex_client
    from quotex_asset_mapper import load_quotex_assets, get_real_quotex_asset_name, print_asset_mapping, check_asset_status
    from utils import print_colored
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Quotex credentials
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"

# Test assets that were previously failing
PROBLEMATIC_ASSETS = [
    "USDBDT_otc",
    "USDARS_otc", 
    "USDEGP_otc",
    "USDINR_otc",
    "USDMXN_otc",
    "USDNGN_otc",
    "USDPKR_otc"
]

async def test_connection_and_mapping():
    """Test connection and asset mapping loading"""
    print_colored("\n🧪 Testing Quotex Connection and Asset Mapping...", "INFO", bold=True)
    
    try:
        # Create browser-based client
        print_colored("🔗 Creating Quotex client...", "INFO")
        client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=True)
        
        # Connect
        print_colored("🔐 Connecting to Quotex...", "INFO")
        connected = await client.connect()
        
        if not connected:
            print_colored("❌ Connection failed", "ERROR")
            return None
        
        print_colored("✅ Connected successfully!", "SUCCESS")
        
        # Load asset mapping
        print_colored("🔍 Loading real asset names from Quotex...", "INFO")
        mapping_loaded = await load_quotex_assets(client)
        
        if mapping_loaded:
            print_colored("✅ Asset mapping loaded successfully!", "SUCCESS")
            return client
        else:
            print_colored("❌ Failed to load asset mapping", "ERROR")
            return None
            
    except Exception as e:
        print_colored(f"❌ Error: {e}", "ERROR")
        return None

async def test_asset_mapping(client):
    """Test asset name mapping for problematic assets"""
    print_colored("\n🔍 Testing Asset Name Mapping...", "INFO", bold=True)
    
    # Print the full mapping
    print_asset_mapping()
    
    print_colored("\n🧪 Testing Problematic Assets...", "WARNING", bold=True)
    
    found_assets = []
    missing_assets = []
    
    for asset in PROBLEMATIC_ASSETS:
        print_colored(f"\n📊 Testing {asset}...", "INFO")
        
        # Get real Quotex name
        quotex_name = get_real_quotex_asset_name(asset)
        
        if quotex_name:
            print_colored(f"✅ {asset} → {quotex_name}", "SUCCESS")
            
            # Check availability
            is_available, verified_name = await check_asset_status(client, asset)
            
            if is_available:
                print_colored(f"✅ {asset}: Available for trading", "SUCCESS")
                found_assets.append((asset, quotex_name))
            else:
                print_colored(f"⚠️ {asset}: Found but not available for trading", "WARNING")
                found_assets.append((asset, quotex_name))
        else:
            print_colored(f"❌ {asset}: No mapping found", "ERROR")
            missing_assets.append(asset)
    
    return found_assets, missing_assets

async def test_data_fetching(client, found_assets):
    """Test data fetching for found assets"""
    if not found_assets:
        print_colored("\n❌ No assets found to test data fetching", "ERROR")
        return
    
    print_colored(f"\n🧪 Testing Data Fetching for {len(found_assets)} Found Assets...", "INFO", bold=True)
    
    from real_data_fetcher import fetch_guaranteed_real_data
    
    successful_fetches = []
    failed_fetches = []
    
    for bot_name, quotex_name in found_assets[:3]:  # Test first 3 to save time
        print_colored(f"\n📊 Testing data fetch for {bot_name} ({quotex_name})...", "INFO")
        
        try:
            df = await fetch_guaranteed_real_data(client, bot_name, "M1", 10)
            
            if df is not None and len(df) > 0:
                print_colored(f"✅ {bot_name}: Data fetched successfully ({len(df)} candles)", "SUCCESS")
                
                # Show sample data
                latest = df.iloc[-1]
                print_colored(f"   Latest: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}", "INFO")
                
                successful_fetches.append(bot_name)
            else:
                print_colored(f"❌ {bot_name}: No data received", "ERROR")
                failed_fetches.append(bot_name)
                
        except Exception as e:
            print_colored(f"❌ {bot_name}: Error - {e}", "ERROR")
            failed_fetches.append(bot_name)
    
    return successful_fetches, failed_fetches

async def main():
    """Run comprehensive asset mapper test"""
    print_colored("🚀 QUOTEX ASSET MAPPER TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 Testing real asset name mapping and data fetching", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Test connection and mapping
    client = await test_connection_and_mapping()
    
    if not client:
        print_colored("\n❌ CONNECTION OR MAPPING FAILED - Cannot proceed", "ERROR", bold=True)
        return
    
    # Test asset mapping
    found_assets, missing_assets = await test_asset_mapping(client)
    
    # Test data fetching for found assets
    if found_assets:
        successful_fetches, failed_fetches = await test_data_fetching(client, found_assets)
    else:
        successful_fetches, failed_fetches = [], []
    
    # Summary
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📊 ASSET MAPPER TEST RESULTS", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    total_tested = len(PROBLEMATIC_ASSETS)
    found_count = len(found_assets)
    missing_count = len(missing_assets)
    
    print_colored(f"\n🔍 Asset Mapping Results:", "INFO", bold=True)
    print_colored(f"   Total tested: {total_tested}", "INFO")
    print_colored(f"   ✅ Found mappings: {found_count}", "SUCCESS" if found_count > 0 else "ERROR")
    print_colored(f"   ❌ Missing mappings: {missing_count}", "ERROR" if missing_count > 0 else "SUCCESS")
    
    if found_assets:
        print_colored(f"\n✅ Assets with mappings found:", "SUCCESS")
        for bot_name, quotex_name in found_assets:
            print_colored(f"   {bot_name} → {quotex_name}", "INFO")
    
    if missing_assets:
        print_colored(f"\n❌ Assets without mappings:", "ERROR")
        for asset in missing_assets:
            print_colored(f"   {asset}", "WARNING")
    
    if successful_fetches or failed_fetches:
        print_colored(f"\n📊 Data Fetching Results:", "INFO", bold=True)
        if successful_fetches:
            print_colored(f"   ✅ Successful fetches: {len(successful_fetches)}", "SUCCESS")
            for asset in successful_fetches:
                print_colored(f"      {asset}", "SUCCESS")
        
        if failed_fetches:
            print_colored(f"   ❌ Failed fetches: {len(failed_fetches)}", "ERROR")
            for asset in failed_fetches:
                print_colored(f"      {asset}", "WARNING")
    
    # Final verdict
    if found_count >= total_tested * 0.5:  # 50% found
        print_colored(f"\n🎉 SUCCESS: Asset mapper found {found_count}/{total_tested} problematic assets!", "SUCCESS", bold=True)
        print_colored("✅ Real asset names are now being used!", "SUCCESS", bold=True)
        
        if found_count == total_tested:
            print_colored("🎯 PERFECT: All problematic assets now have real mappings!", "SUCCESS", bold=True)
        else:
            print_colored(f"⚠️ {missing_count} assets may not be available on Quotex", "WARNING")
    else:
        print_colored(f"⚠️ PARTIAL: Only {found_count}/{total_tested} assets found", "WARNING", bold=True)
        print_colored("❌ Some assets may not be available on this Quotex account", "ERROR")
    
    print_colored("=" * 80, "SKY_BLUE")

if __name__ == "__main__":
    asyncio.run(main())
