#!/usr/bin/env python3
"""
Test script to verify live pairs work correctly with Oanda API
Tests real data fetching, strategy analysis, and signal generation for live pairs
"""

import sys
import os
import asyncio
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils import fetch_live_candles, fetch_historical_candles, get_oanda_headers, print_colored
    from Model import convert_quotex_to_oanda_pair, fetch_quotex_market_data, generate_signal
    from strategy_engine import StrategyEngine
    from data_validator import validate_market_data, log_validation_result
    from config import OANDA_CONFIG
    import requests
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Test live pairs that should use Oanda API
LIVE_PAIRS = [
    "EURUSD",      # Major pair
    "GBPUSD",      # Major pair
    "USDJPY",      # Major pair
    "AUDUSD",      # Major pair
    "USDCAD",      # Major pair
    "EURGBP",      # Cross pair
    "EURJPY",      # Cross pair
    "GBPJPY",      # Cross pair
]

async def test_oanda_connection():
    """Test Oanda API connection and authentication"""
    print_colored("\n🧪 Testing Oanda API Connection...", "INFO", bold=True)
    
    try:
        headers = get_oanda_headers()
        
        # Test connection with account info
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}"
        
        print_colored("🔗 Testing Oanda API authentication...", "INFO")
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            account_data = response.json()
            account_info = account_data.get('account', {})
            
            print_colored("✅ Oanda API connection successful!", "SUCCESS")
            print_colored(f"   Account ID: {account_info.get('id', 'Unknown')}", "INFO")
            print_colored(f"   Currency: {account_info.get('currency', 'Unknown')}", "INFO")
            print_colored(f"   Balance: {account_info.get('balance', 'Unknown')}", "INFO")
            
            return True
        else:
            print_colored(f"❌ Oanda API connection failed: {response.status_code}", "ERROR")
            print_colored(f"   Response: {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ Oanda connection error: {e}", "ERROR")
        return False

async def test_live_data_fetching(pair):
    """Test live data fetching for a specific pair"""
    print_colored(f"\n📊 Testing {pair}...", "INFO")
    
    try:
        # Convert to Oanda format
        oanda_pair = convert_quotex_to_oanda_pair(pair)
        
        if not oanda_pair:
            print_colored(f"❌ {pair}: No Oanda mapping found", "ERROR")
            return False
        
        print_colored(f"   Oanda pair: {oanda_pair}", "INFO")
        
        # Test live candle fetching
        print_colored(f"🔍 Fetching live candles for {oanda_pair}...", "INFO")
        df = fetch_live_candles(oanda_pair, count=50, granularity="M1")
        
        if df is None or len(df) == 0:
            print_colored(f"❌ {pair}: No live data received", "ERROR")
            return False
        
        print_colored(f"✅ {pair}: Got {len(df)} live candles", "SUCCESS")
        
        # Validate data quality
        validation_result = validate_market_data(df, pair)
        
        if not validation_result['is_valid']:
            print_colored(f"❌ {pair}: Data validation failed", "ERROR")
            log_validation_result(validation_result)
            return False
        
        print_colored(f"✅ {pair}: Data validation passed", "SUCCESS")
        
        # Show sample data
        latest = df.iloc[-1]
        print_colored(f"   Latest candle: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}", "INFO")
        print_colored(f"   Volume: {latest['volume']:.0f} | Timestamp: {latest['timestamp']:.0f}", "INFO")
        
        # Test historical data
        print_colored(f"🔍 Fetching historical candles for {oanda_pair}...", "INFO")
        hist_df = fetch_historical_candles(oanda_pair, count=100, granularity="M1")
        
        if hist_df is not None and len(hist_df) > 0:
            print_colored(f"✅ {pair}: Got {len(hist_df)} historical candles", "SUCCESS")
        else:
            print_colored(f"⚠️ {pair}: No historical data", "WARNING")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ {pair}: Error - {e}", "ERROR")
        return False

async def test_strategy_analysis(pair):
    """Test strategy analysis and signal generation for a live pair"""
    print_colored(f"\n🧠 Testing Strategy Analysis for {pair}...", "INFO")
    
    try:
        # Initialize strategy engine
        strategy_engine = StrategyEngine()
        
        # Fetch data using the main data fetching function
        print_colored(f"📊 Fetching market data for analysis...", "INFO")
        df = await fetch_quotex_market_data(pair, "M1", 50)
        
        if df is None or len(df) == 0:
            print_colored(f"❌ {pair}: No data for strategy analysis", "ERROR")
            return False
        
        print_colored(f"✅ {pair}: Got {len(df)} candles for analysis", "SUCCESS")
        
        # Test each strategy
        strategies = ["S1", "S2", "S3", "S4", "S5"]
        strategy_results = {}
        
        for strategy_id in strategies:
            try:
                print_colored(f"🔍 Testing {strategy_id}...", "INFO")
                
                # Generate signal using strategy
                signal, confidence, price, used_strategy, indicators, market_analysis = await generate_signal(
                    pair, strategy_engine, [strategy_id], "M1", 15, 50, df
                )
                
                strategy_results[strategy_id] = {
                    'signal': signal,
                    'confidence': confidence,
                    'price': price,
                    'indicators': indicators,
                    'market_analysis': market_analysis
                }
                
                print_colored(f"   {strategy_id}: {signal.upper()} | {confidence:.1%} | ${price:.5f}", "SUCCESS")
                
            except Exception as e:
                print_colored(f"   {strategy_id}: Error - {e}", "ERROR")
                strategy_results[strategy_id] = None
        
        # Show detailed analysis for best strategy
        best_strategy = max(strategy_results.items(), 
                          key=lambda x: x[1]['confidence'] if x[1] else 0)
        
        if best_strategy[1]:
            print_colored(f"\n🎯 Best Strategy Analysis ({best_strategy[0]}):", "INFO", bold=True)
            result = best_strategy[1]
            
            print_colored(f"   Signal: {result['signal'].upper()}", "SUCCESS")
            print_colored(f"   Confidence: {result['confidence']:.1%}", "SUCCESS")
            print_colored(f"   Price: ${result['price']:.5f}", "INFO")
            
            # Show key indicators
            if result['indicators']:
                print_colored(f"   RSI: {result['indicators'].get('rsi', 'N/A'):.1f}", "INFO")
                print_colored(f"   MACD: {result['indicators'].get('macd', 'N/A'):.5f}", "INFO")
                print_colored(f"   BB Position: {result['indicators'].get('bb_position', 'N/A')}", "INFO")
            
            # Show market analysis
            if result['market_analysis']:
                # Use correct keys from market analysis
                trend = result['market_analysis'].get('market_trend', 'N/A')
                support = result['market_analysis'].get('support_level', 'N/A')
                resistance = result['market_analysis'].get('resistance_level', 'N/A')

                print_colored(f"   Trend: {trend}", "INFO")

                if isinstance(support, (int, float)):
                    print_colored(f"   Support: ${support:.5f}", "INFO")
                else:
                    print_colored(f"   Support: {support}", "INFO")

                if isinstance(resistance, (int, float)):
                    print_colored(f"   Resistance: ${resistance:.5f}", "INFO")
                else:
                    print_colored(f"   Resistance: {resistance}", "INFO")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ {pair}: Strategy analysis error - {e}", "ERROR")
        return False

async def main():
    """Run comprehensive live pairs test"""
    print_colored("🚀 LIVE PAIRS OANDA API TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 Testing live pairs with real Oanda data and strategy analysis", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Test Oanda connection
    oanda_connected = await test_oanda_connection()
    
    if not oanda_connected:
        print_colored("\n❌ OANDA CONNECTION FAILED - Cannot proceed", "ERROR", bold=True)
        return
    
    print_colored("\n🧪 Testing Live Data Fetching...", "INFO", bold=True)
    
    data_results = {}
    for pair in LIVE_PAIRS:
        success = await test_live_data_fetching(pair)
        data_results[pair] = success
    
    print_colored("\n🧪 Testing Strategy Analysis...", "INFO", bold=True)
    
    strategy_results = {}
    # Test strategy analysis for first 3 pairs to save time
    for pair in LIVE_PAIRS[:3]:
        if data_results.get(pair, False):  # Only test if data fetching worked
            success = await test_strategy_analysis(pair)
            strategy_results[pair] = success
        else:
            strategy_results[pair] = False
    
    # Summary
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📊 LIVE PAIRS TEST RESULTS", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Data fetching results
    data_success = sum(1 for success in data_results.values() if success)
    data_total = len(data_results)
    
    print_colored(f"\n📊 Data Fetching Results:", "INFO", bold=True)
    print_colored(f"   Total tested: {data_total}", "INFO")
    print_colored(f"   ✅ Successful: {data_success}", "SUCCESS" if data_success > 0 else "ERROR")
    print_colored(f"   ❌ Failed: {data_total - data_success}", "ERROR" if data_total - data_success > 0 else "SUCCESS")
    
    if data_success > 0:
        print_colored(f"\n✅ Working pairs:", "SUCCESS")
        for pair, success in data_results.items():
            if success:
                print_colored(f"   {pair} → {convert_quotex_to_oanda_pair(pair)}", "SUCCESS")
    
    if data_total - data_success > 0:
        print_colored(f"\n❌ Failed pairs:", "ERROR")
        for pair, success in data_results.items():
            if not success:
                print_colored(f"   {pair}", "WARNING")
    
    # Strategy analysis results
    strategy_success = sum(1 for success in strategy_results.values() if success)
    strategy_total = len(strategy_results)
    
    if strategy_total > 0:
        print_colored(f"\n🧠 Strategy Analysis Results:", "INFO", bold=True)
        print_colored(f"   Total tested: {strategy_total}", "INFO")
        print_colored(f"   ✅ Successful: {strategy_success}", "SUCCESS" if strategy_success > 0 else "ERROR")
        print_colored(f"   ❌ Failed: {strategy_total - strategy_success}", "ERROR" if strategy_total - strategy_success > 0 else "SUCCESS")
    
    # Final verdict
    overall_success_rate = (data_success + strategy_success) / (data_total + strategy_total) if (data_total + strategy_total) > 0 else 0
    
    if overall_success_rate >= 0.8:  # 80% success rate
        print_colored(f"\n🎉 EXCELLENT: Live pairs working perfectly! ({overall_success_rate:.0%} success)", "SUCCESS", bold=True)
        print_colored("✅ Real Oanda data fetching is functional!", "SUCCESS", bold=True)
        print_colored("✅ Strategy analysis working correctly!", "SUCCESS", bold=True)
        print_colored("✅ Signals based on authentic market data!", "SUCCESS", bold=True)
    elif overall_success_rate >= 0.6:  # 60% success rate
        print_colored(f"\n✅ GOOD: Most live pairs working ({overall_success_rate:.0%} success)", "SUCCESS", bold=True)
        print_colored("⚠️ Some pairs may need attention", "WARNING")
    else:
        print_colored(f"\n⚠️ ISSUES: Live pairs need fixes ({overall_success_rate:.0%} success)", "WARNING", bold=True)
        print_colored("❌ Check Oanda API configuration and pair mappings", "ERROR")
    
    print_colored("=" * 80, "SKY_BLUE")

if __name__ == "__main__":
    asyncio.run(main())
