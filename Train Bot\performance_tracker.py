#!/usr/bin/env python3
"""
Performance Tracker Module for Trading Bot
Tracks win/loss statistics, generates performance summaries, and displays performance metrics
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from utils import print_colored

class PerformanceTracker:
    def __init__(self):
        """Initialize performance tracker"""
        self.performance_data = {
            'pairs_data': {},
            'overall': {'total': 0, 'wins': 0, 'losses': 0},
            'daily_stats': {},
            'session_start': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def update_performance(self, signal_data: Dict[str, Any]) -> bool:
        """Update performance statistics with signal result"""
        try:
            if signal_data['result'] not in ['win', 'loss']:
                return False
            
            pair = signal_data['pair']
            result = signal_data['result']
            date = signal_data['date']
            
            # Initialize pair data if not exists
            if pair not in self.performance_data['pairs_data']:
                self.performance_data['pairs_data'][pair] = {
                    'total': 0, 'wins': 0, 'losses': 0
                }
            
            # Initialize daily stats if not exists
            if date not in self.performance_data['daily_stats']:
                self.performance_data['daily_stats'][date] = {
                    'total': 0, 'wins': 0, 'losses': 0
                }
            
            # Update pair statistics
            self.performance_data['pairs_data'][pair]['total'] += 1
            if result == 'win':
                self.performance_data['pairs_data'][pair]['wins'] += 1
            else:
                self.performance_data['pairs_data'][pair]['losses'] += 1
            
            # Update overall statistics
            self.performance_data['overall']['total'] += 1
            if result == 'win':
                self.performance_data['overall']['wins'] += 1
            else:
                self.performance_data['overall']['losses'] += 1
            
            # Update daily statistics
            self.performance_data['daily_stats'][date]['total'] += 1
            if result == 'win':
                self.performance_data['daily_stats'][date]['wins'] += 1
            else:
                self.performance_data['daily_stats'][date]['losses'] += 1
            
            return True
            
        except Exception as e:
            print_colored(f"❌ Error updating performance: {e}", "ERROR")
            return False
    
    def get_performance_summary(self, selected_pairs: Optional[List[str]] = None) -> Dict[str, Any]:
        """Get current performance summary"""
        try:
            summary = {
                'total_pairs': len(self.performance_data['pairs_data']),
                'pairs_data': {},
                'overall': self.performance_data['overall'].copy(),
                'win_rate': 0.0,
                'session_start': self.performance_data['session_start']
            }
            
            # Filter pairs if specified
            if selected_pairs:
                for pair in selected_pairs:
                    if pair in self.performance_data['pairs_data']:
                        summary['pairs_data'][pair] = self.performance_data['pairs_data'][pair].copy()
                    else:
                        # Initialize empty stats for selected pairs with no signals yet
                        summary['pairs_data'][pair] = {'total': 0, 'wins': 0, 'losses': 0}
                summary['total_pairs'] = len(selected_pairs)
            else:
                summary['pairs_data'] = self.performance_data['pairs_data'].copy()
            
            # Calculate overall win rate
            if summary['overall']['total'] > 0:
                summary['win_rate'] = (summary['overall']['wins'] / summary['overall']['total']) * 100
            
            return summary
            
        except Exception as e:
            print_colored(f"❌ Error getting performance summary: {e}", "ERROR")
            return self._default_summary()
    
    def print_performance_box(self, selected_pairs: List[str]) -> None:
        """Display the performance summary box"""
        try:
            summary = self.get_performance_summary(selected_pairs)
            
            print_colored("\n" + "=" * 80, "SKY_BLUE")
            print_colored("📊 SIGNAL PERFORMANCE SUMMARY", "OCEAN", bold=True)
            print_colored("=" * 80, "SKY_BLUE")
            
            # Header info
            total_pairs = len(selected_pairs)
            pair_names = ", ".join(selected_pairs[:2])
            if len(selected_pairs) > 2:
                pair_names += f", +{len(selected_pairs) - 2} more"
            
            print_colored(f"Total Pairs Selected: {total_pairs} ({pair_names})", "INFO")
            print()
            
            # Table header
            header = f"{'Pairs':<20} | {'Total Signals':<13} | {'Wins':<6} | {'Losses':<8} | {'Win Rate':<8}"
            print_colored(header, "OCEAN", bold=True)
            print_colored("-" * 70, "SKY_BLUE")
            
            # Pair statistics
            for pair in selected_pairs:
                pair_data = summary['pairs_data'].get(pair, {'total': 0, 'wins': 0, 'losses': 0})
                
                total = pair_data['total']
                wins = pair_data['wins']
                losses = pair_data['losses']
                win_rate = (wins / total * 100) if total > 0 else 0.0
                
                # Format display
                total_str = f"{total:02d}" if total > 0 else "00"
                wins_str = f"{wins:02d}" if wins > 0 else "00"
                losses_str = f"{losses:02d}" if losses > 0 else "00"
                win_rate_str = f"{win_rate:.1f}%" if total > 0 else "0.0%"
                
                # Color based on performance
                if total == 0:
                    color = "INFO"
                elif win_rate >= 70:
                    color = "SUCCESS"
                elif win_rate >= 50:
                    color = "WARNING"
                else:
                    color = "ERROR"
                
                row = f"{pair:<20} | {total_str:>13} | {wins_str:>6} | {losses_str:>8} | {win_rate_str:>8}"
                print_colored(row, color)
            
            # Overall statistics
            print_colored("-" * 70, "SKY_BLUE")
            overall = summary['overall']
            overall_total = overall['total']
            overall_wins = overall['wins']
            overall_losses = overall['losses']
            overall_win_rate = summary['win_rate']
            
            total_str = f"{overall_total:02d}" if overall_total > 0 else "00"
            wins_str = f"{overall_wins:02d}" if overall_wins > 0 else "00"
            losses_str = f"{overall_losses:02d}" if overall_losses > 0 else "00"
            win_rate_str = f"{overall_win_rate:.1f}%" if overall_total > 0 else "0.0%"
            
            # Color for overall performance
            if overall_total == 0:
                overall_color = "INFO"
            elif overall_win_rate >= 70:
                overall_color = "SUCCESS"
            elif overall_win_rate >= 50:
                overall_color = "WARNING"
            else:
                overall_color = "ERROR"
            
            overall_row = f"{'Overall':<20} | {total_str:>13} | {wins_str:>6} | {losses_str:>8} | {win_rate_str:>8}"
            print_colored(overall_row, overall_color, bold=True)
            
            print_colored("=" * 80, "SKY_BLUE")
            
        except Exception as e:
            print_colored(f"❌ Error displaying performance box: {e}", "ERROR")
    
    def get_daily_performance(self, date: str) -> Dict[str, Any]:
        """Get performance for a specific date"""
        return self.performance_data['daily_stats'].get(date, {
            'total': 0, 'wins': 0, 'losses': 0
        })
    
    def reset_session_performance(self) -> None:
        """Reset performance data for new session"""
        self.performance_data = {
            'pairs_data': {},
            'overall': {'total': 0, 'wins': 0, 'losses': 0},
            'daily_stats': {},
            'session_start': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        print_colored("🔄 Performance data reset for new session", "INFO")
    
    def get_pair_performance(self, pair: str) -> Dict[str, Any]:
        """Get performance for a specific pair"""
        return self.performance_data['pairs_data'].get(pair, {
            'total': 0, 'wins': 0, 'losses': 0
        })
    
    def export_performance_summary(self) -> Dict[str, Any]:
        """Export complete performance data"""
        return {
            'performance_data': self.performance_data.copy(),
            'export_timestamp': datetime.now().isoformat(),
            'summary': self.get_performance_summary()
        }
    
    def _default_summary(self) -> Dict[str, Any]:
        """Return default summary when error occurs"""
        return {
            'total_pairs': 0,
            'pairs_data': {},
            'overall': {'total': 0, 'wins': 0, 'losses': 0},
            'win_rate': 0.0,
            'session_start': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

# Global performance tracker instance
performance_tracker = PerformanceTracker()

# Convenience functions for easy access
def update_performance(signal_data: Dict[str, Any]) -> bool:
    """Update performance using global tracker"""
    return performance_tracker.update_performance(signal_data)

def get_performance_summary(selected_pairs: Optional[List[str]] = None) -> Dict[str, Any]:
    """Get performance summary using global tracker"""
    return performance_tracker.get_performance_summary(selected_pairs)

def print_performance_box(selected_pairs: List[str]) -> None:
    """Print performance box using global tracker"""
    performance_tracker.print_performance_box(selected_pairs)

def reset_session_performance() -> None:
    """Reset session performance using global tracker"""
    performance_tracker.reset_session_performance()

def get_pair_performance(pair: str) -> Dict[str, Any]:
    """Get pair performance using global tracker"""
    return performance_tracker.get_pair_performance(pair)
