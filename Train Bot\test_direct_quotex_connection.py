#!/usr/bin/env python3
"""
Test script to verify direct PyQuotex API connection and real data fetching
This will test the new direct connection method without browser integration
"""

import sys
import os
import asyncio
from datetime import datetime

# Add parent directory to path for PyQuotex imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pyquotex.stable_api import Quotex
    from utils import print_colored
    from data_validator import validate_market_data, log_validation_result
    print_colored("✅ All modules imported successfully!", "SUCCESS")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Quotex credentials
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"

# Test assets with their Quotex API names
TEST_ASSETS = [
    ("EURUSD_otc", "EURUSD"),
    ("GBPUSD_otc", "GBPUSD"),
    ("USDJPY_otc", "USDJPY"),
    ("USDBDT_otc", "USD/BDT"),
    ("USDEGP_otc", "USD/EGP"),
    ("USDNGN_otc", "USD/NGN"),
    ("USDPKR_otc", "USD/PKR"),
    ("USDINR_otc", "USD/INR"),
    ("USDMXN_otc", "USD/MXN"),
    ("XAUUSD_otc", "XAUUSD"),
]

async def test_direct_connection():
    """Test direct PyQuotex API connection"""
    print_colored("\n🧪 Testing Direct PyQuotex API Connection...", "INFO", bold=True)
    
    try:
        # Create direct PyQuotex client
        print_colored("🔗 Creating direct PyQuotex client...", "INFO")
        client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)
        
        # Connect to Quotex API
        print_colored("🔐 Connecting to Quotex API...", "INFO")
        check_connect, message = await client.connect()
        
        if check_connect:
            print_colored("✅ Direct PyQuotex connection successful!", "SUCCESS")
            print_colored(f"🔗 Connection status: {client.check_connect}", "INFO")
            
            # Get balance to verify connection
            try:
                balance = await client.get_balance()
                print_colored(f"💰 Account balance: ${balance:.2f}", "SUCCESS")
            except Exception as e:
                print_colored(f"⚠️ Balance check failed: {e}", "WARNING")
            
            return client
        else:
            print_colored(f"❌ Connection failed: {message}", "ERROR")
            return None
            
    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")
        return None

async def test_data_fetching(client, asset_pair, quotex_name):
    """Test data fetching for a specific asset"""
    print_colored(f"\n📊 Testing {asset_pair} (API: {quotex_name})...", "INFO")
    
    try:
        import time
        
        # Parameters
        period = 60  # 1 minute
        count = 50   # 50 candles
        end_time = time.time()
        offset = count * period
        
        # Method 1: Try get_candles
        print_colored(f"🔍 Trying get_candles for {quotex_name}...", "INFO")
        candles = await client.get_candles(quotex_name, end_time, offset, period)
        
        if candles and len(candles) > 0:
            print_colored(f"✅ get_candles success: {len(candles)} candles received", "SUCCESS")
            
            # Convert to DataFrame format for validation
            candles_data = []
            for candle in candles:
                if isinstance(candle, dict):
                    candles_data.append({
                        'open': float(candle.get('open', candle.get('o', 0))),
                        'high': float(candle.get('high', candle.get('h', 0))),
                        'low': float(candle.get('low', candle.get('l', 0))),
                        'close': float(candle.get('close', candle.get('c', 0))),
                        'volume': int(candle.get('volume', candle.get('v', candle.get('ticks', 1000)))),
                        'timestamp': candle.get('time', candle.get('timestamp', time.time()))
                    })
                elif isinstance(candle, (list, tuple)) and len(candle) >= 5:
                    candles_data.append({
                        'open': float(candle[1]),
                        'high': float(candle[2]),
                        'low': float(candle[3]),
                        'close': float(candle[4]),
                        'volume': int(candle[5]) if len(candle) > 5 else 1000,
                        'timestamp': candle[0]
                    })
            
            if candles_data:
                import pandas as pd
                df = pd.DataFrame(candles_data)
                
                # Validate data quality
                validation_result = validate_market_data(df, asset_pair)
                
                if validation_result['is_valid']:
                    print_colored(f"✅ {asset_pair}: Data validation PASSED", "SUCCESS")
                    
                    # Show sample data
                    if len(df) > 0:
                        latest = df.iloc[-1]
                        print_colored(f"   Latest candle: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}", "INFO")
                    
                    return True
                else:
                    print_colored(f"❌ {asset_pair}: Data validation FAILED", "ERROR")
                    log_validation_result(validation_result)
                    return False
            else:
                print_colored(f"❌ {asset_pair}: Could not convert candle data", "ERROR")
                return False
        else:
            print_colored(f"❌ get_candles failed: No data received", "ERROR")
            
            # Method 2: Try get_candle_v2 as fallback
            print_colored(f"🔍 Trying get_candle_v2 for {quotex_name}...", "INFO")
            try:
                candles_v2 = await client.get_candle_v2(quotex_name, period)
                if candles_v2 and len(candles_v2) > 0:
                    print_colored(f"✅ get_candle_v2 success: {len(candles_v2)} candles received", "SUCCESS")
                    return True
                else:
                    print_colored(f"❌ get_candle_v2 also failed", "ERROR")
                    return False
            except Exception as e:
                print_colored(f"❌ get_candle_v2 error: {e}", "ERROR")
                return False
            
    except Exception as e:
        print_colored(f"❌ Data fetching error for {asset_pair}: {e}", "ERROR")
        return False

async def main():
    """Run comprehensive direct PyQuotex API test"""
    print_colored("🚀 DIRECT PYQUOTEX API CONNECTION TEST", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 Testing direct API connection without browser integration", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Test connection
    client = await test_direct_connection()
    
    if not client:
        print_colored("\n❌ CONNECTION FAILED - Cannot proceed with data tests", "ERROR", bold=True)
        return
    
    print_colored("\n🧪 Testing Data Fetching for Multiple Assets...", "INFO", bold=True)
    
    successful_pairs = []
    failed_pairs = []
    
    # Test data fetching for each asset
    for asset_pair, quotex_name in TEST_ASSETS:
        try:
            success = await test_data_fetching(client, asset_pair, quotex_name)
            
            if success:
                successful_pairs.append(asset_pair)
            else:
                failed_pairs.append(asset_pair)
                
        except Exception as e:
            print_colored(f"❌ Test error for {asset_pair}: {e}", "ERROR")
            failed_pairs.append(asset_pair)
    
    # Summary
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("📊 TEST RESULTS SUMMARY", "INFO", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    total_tested = len(TEST_ASSETS)
    successful_count = len(successful_pairs)
    failed_count = len(failed_pairs)
    
    print_colored(f"Total Assets Tested: {total_tested}", "INFO")
    print_colored(f"✅ Successful: {successful_count}", "SUCCESS")
    print_colored(f"❌ Failed: {failed_count}", "ERROR" if failed_count > 0 else "SUCCESS")
    
    if successful_pairs:
        print_colored("\n✅ Successfully fetched data for:", "SUCCESS")
        for pair in successful_pairs:
            print_colored(f"   - {pair}", "INFO")
    
    if failed_pairs:
        print_colored("\n❌ Failed to fetch data for:", "ERROR")
        for pair in failed_pairs:
            print_colored(f"   - {pair}", "WARNING")
    
    # Final verdict
    if successful_count >= total_tested * 0.7:  # 70% success rate
        print_colored(f"\n🎉 OVERALL: SUCCESS! ({successful_count}/{total_tested} pairs working)", "SUCCESS", bold=True)
        print_colored("✅ Direct PyQuotex API connection is working!", "SUCCESS", bold=True)
        print_colored("✅ Real data fetching is functional!", "SUCCESS", bold=True)
    else:
        print_colored(f"\n⚠️ OVERALL: PARTIAL SUCCESS ({successful_count}/{total_tested} pairs working)", "WARNING", bold=True)
        print_colored("❌ Some pairs may need different API names or methods", "WARNING")
    
    print_colored("=" * 80, "SKY_BLUE")

if __name__ == "__main__":
    asyncio.run(main())
