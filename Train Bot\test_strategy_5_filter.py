#!/usr/bin/env python3
"""
Test Script for Strategy 5 Market Condition Filter
Demonstrates how the filter prevents Strategy 5 execution during sideways markets
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_5_filter_manager import Strategy5FilterManager
from utils import print_colored

def test_with_august_8_data():
    """Test the filter using the August 8th signals data"""
    print_colored("🧪 Testing Strategy 5 Filter with August 8th Data", "HEADER", bold=True)
    print_colored("=" * 60, "HEADER")
    
    # Initialize the filter manager
    manager = Strategy5FilterManager()
    
    # Show current configuration
    manager.show_current_config()
    
    # Analyze the August 8th signals file
    signals_file = "signals/signals_2025-08-08.json"
    
    if os.path.exists(signals_file):
        manager.analyze_signals_file(signals_file)
    else:
        print_colored(f"❌ Signals file not found: {signals_file}", "ERROR")
        print_colored("Please ensure the signals file exists in the correct location", "WARNING")
        return
    
    # Demonstrate different threshold settings
    print_colored("\n🔬 Testing Different Threshold Settings", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    thresholds_to_test = [0.4, 0.5, 0.6, 0.7, 0.8]
    
    for threshold in thresholds_to_test:
        print_colored(f"\n📊 Threshold: {threshold:.1f}", "INFO", bold=True)
        
        if threshold <= 0.5:
            print_colored("  → Would allow execution in sideways markets", "WARNING")
            print_colored("  → Higher risk of losses during market condition changes", "ERROR")
        elif threshold <= 0.6:
            print_colored("  → Balanced approach - filters weak trends", "INFO")
            print_colored("  → Recommended setting for most users", "SUCCESS")
        elif threshold <= 0.7:
            print_colored("  → Conservative approach - requires moderate trends", "INFO")
            print_colored("  → Fewer signals but higher quality", "SUCCESS")
        else:
            print_colored("  → Very conservative - only strong trends", "WARNING")
            print_colored("  → May miss profitable opportunities", "WARNING")
    
    print_colored("\n💡 Recommendations", "HEADER", bold=True)
    print_colored("=" * 30, "HEADER")
    print_colored("• Default threshold (0.6) is recommended for most users", "SUCCESS")
    print_colored("• Use 0.7+ for more conservative trading", "INFO")
    print_colored("• Avoid thresholds below 0.6 to prevent sideways market losses", "WARNING")
    print_colored("• Enable debug mode to see filter decisions in real-time", "INFO")

def demonstrate_filter_benefits():
    """Demonstrate the benefits of the Strategy 5 filter"""
    print_colored("\n🎯 Strategy 5 Filter Benefits", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    benefits = [
        "Prevents Strategy 5 execution during sideways markets",
        "Reduces losses when market conditions change from trending to sideways",
        "Maintains Strategy 5's effectiveness in its optimal conditions",
        "Configurable threshold allows fine-tuning for different risk preferences",
        "Debug mode provides transparency in filtering decisions",
        "Preserves capital for better trading opportunities"
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print_colored(f"{i}. {benefit}", "SUCCESS")
    
    print_colored("\n⚙️ How It Works", "HEADER", bold=True)
    print_colored("=" * 20, "HEADER")
    
    steps = [
        "Analyzes market trend using EMA crossover and price movement",
        "Calculates trend strength based on price change over 20 periods",
        "Compares trend strength to configured threshold (default: 0.6)",
        "Allows Strategy 5 execution only if trend strength >= threshold",
        "Returns no signal (0, 0.0) if market conditions don't meet requirements"
    ]
    
    for i, step in enumerate(steps, 1):
        print_colored(f"{i}. {step}", "INFO")

def main():
    """Main test function"""
    print_colored("🚀 Strategy 5 Market Condition Filter Test Suite", "HEADER", bold=True)
    print_colored("=" * 70, "HEADER")
    
    # Test with August 8th data
    test_with_august_8_data()
    
    # Demonstrate filter benefits
    demonstrate_filter_benefits()
    
    print_colored("\n✅ Test Complete!", "SUCCESS", bold=True)
    print_colored("The Strategy 5 filter is now active and will prevent execution during sideways markets.", "SUCCESS")
    print_colored("\nTo manage the filter settings, run: python strategy_5_filter_manager.py", "INFO")

if __name__ == "__main__":
    main()
