# 🔮 Advance Signal Analyzer - Complete Guide

## 📋 Overview

The Advance Signal Analyzer is a powerful tool that analyzes time-based candle behavior over multiple working days to find repeating time slots where all selected days had candles with the same direction (UP/DOWN), strong bodies, and additional technical confirmation.

## 🚀 How to Access

1. Start the main bot: `python "Train Bot/Model.py"`
2. Enter authentication key: `miketester2390`
3. Select option **4. 🔮 Advance Signal Analysis** from the main menu

## 📊 Features

### ✅ Core Functionality
- **Multi-Day Analysis**: Analyzes N working days (3-10 days)
- **Time-Based Patterns**: Finds repeating time slots with consistent behavior
- **Direction Matching**: Ensures all days have same direction (UP/DOWN) at specific times
- **Candle Strength Validation**: Rejects dojis and pinbars
- **Technical Filters**: RSI, EMA trend, and MA confirmation
- **OANDA Integration**: Uses real market data (no Quotex connection required)

### 🎯 Signal Criteria

#### 1. Same Direction Requirement
- All N days must have candles with same direction at the exact time
- **UP**: close > open on all days
- **DOWN**: close < open on all days
- Doji candles (close = open) are rejected

#### 2. Candle Strength Requirements
- **Body Size**: Must be > 60% of total candle range (high - low)
- **Wick Limits**: Upper and lower wicks must each be < 40% of total range
- Ensures strong, decisive candles (not pinbars or dojis)

#### 3. Technical Confirmation Filters
- **RSI Filter**: 
  - DOWN signals: RSI < 40
  - UP signals: RSI > 60
- **Trend Filter** (EMA20 vs EMA50):
  - DOWN signals: EMA20 < EMA50 (downtrend)
  - UP signals: EMA20 > EMA50 (uptrend)
- **MA Confirmation** (Close vs EMA20):
  - DOWN signals: close < EMA20
  - UP signals: close > EMA20

## 🔧 User Input Guide

### 1. Trading Pair
- **Format**: EUR/USD, GBP/USD, BTC/USD, etc.
- **Supported**: Major forex pairs and popular crypto pairs
- **Example**: `EUR/USD`

### 2. Timeframe Selection
- **1**: 1-minute candles (M1)
- **5**: 5-minute candles (M5) 
- **15**: 15-minute candles (M15)
- **30**: 30-minute candles (M30)
- **60**: 1-hour candles (H1)

### 3. Analysis Period
- **Range**: 3-10 working days
- **Recommendation**: Start with 4-5 days for balance between pattern reliability and data availability
- **Note**: More days = stricter patterns but fewer signals

### 4. Time Range
- **Format**: 24-hour format (HH:MM)
- **Example**: Start: `12:35`, End: `16:35`
- **Recommendation**: Focus on active trading sessions (London: 08:00-17:00, New York: 13:00-22:00)

## 📈 Output Format

### Signal List Example
```
📅 Signal List (for EUR/USD on 5-min timeframe)
12:37 📉 DOWN  
13:12 📈 UP  
14:45 📉 DOWN  
15:01 📈 UP  
```

### Detailed Results
- **Time**: Exact time slot (HH:MM)
- **Direction**: UP (📈) or DOWN (📉)
- **Confidence**: Calculated confidence score (0-100%)
- **Days**: Number of days with matching pattern

## 💾 File Output

### Automatic File Generation
1. **Daily Data**: `advance_signals/day1.csv`, `day2.csv`, etc.
2. **Results CSV**: `advance_signals/signals_YYYYMMDD_HHMMSS.csv`
3. **Text Report**: `advance_signals/signals_YYYYMMDD_HHMMSS.txt`

### CSV Format
```csv
time,direction,candle_count,confidence
12:37,DOWN,5,75.2
13:12,UP,5,82.1
```

## 🎯 Best Practices

### 1. Optimal Settings
- **Timeframe**: 5-15 minutes for good balance
- **Days**: 4-6 days for reliable patterns
- **Time Range**: 2-4 hour windows during active sessions

### 2. Market Sessions
- **London**: 08:00-17:00 GMT (high volatility)
- **New York**: 13:00-22:00 GMT (overlap with London)
- **Asian**: 00:00-09:00 GMT (lower volatility)

### 3. Pair Selection
- **Major Pairs**: EUR/USD, GBP/USD, USD/JPY (most reliable)
- **Cross Pairs**: EUR/GBP, GBP/JPY (good for specific sessions)
- **Crypto**: BTC/USD, ETH/USD (24/7 trading)

## ⚠️ Important Notes

### Working Days Logic
- **Excludes**: Weekends (Saturday, Sunday)
- **Excludes**: US federal holidays
- **Includes**: Only business days with active trading

### Data Requirements
- **Minimum**: 70% of requested days must have data
- **Source**: OANDA API (practice environment)
- **Quality**: Real-time market data with validation

### Performance Tips
- **Smaller time ranges** = faster analysis
- **Fewer days** = more signals but less reliable
- **More days** = fewer signals but higher confidence

## 🔍 Troubleshooting

### No Signals Found
1. **Increase analysis days** (try 6-8 days)
2. **Expand time range** (try 3-4 hour windows)
3. **Change timeframe** (try 5-minute instead of 1-minute)
4. **Different market session** (try London or New York hours)

### Data Fetch Issues
1. Check internet connection
2. Verify OANDA API is accessible
3. Try different trading pair
4. Reduce number of analysis days

### Performance Issues
1. Use shorter time ranges (1-2 hours)
2. Use larger timeframes (15-30 minutes)
3. Reduce number of analysis days

## 📞 Support

If you encounter any issues:
1. Check the generated log files in `advance_signals/` directory
2. Verify OANDA connection with: `python test_oanda.py`
3. Run integration test: `python verify_integration.py`
4. Test with demo: `python demo_advance_signals.py`

---

**🎉 Happy Trading with Advance Signal Analysis!**
