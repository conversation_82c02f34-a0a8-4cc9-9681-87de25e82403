#!/usr/bin/env python3
"""
Strategy 5 Market Condition Filter Manager
Provides tools to configure and test the Strategy 5 market condition filter
"""

import json
import pandas as pd
from datetime import datetime
from utils import print_colored
from config import STRATEGY_5_FILTER_CONFIG
from strategy_engine import StrategyEngine

class Strategy5FilterManager:
    def __init__(self):
        """Initialize the filter manager"""
        self.strategy_engine = StrategyEngine()
        
    def show_current_config(self):
        """Display current Strategy 5 filter configuration"""
        print_colored("\n📊 Strategy 5 Market Condition Filter Configuration", "HEADER", bold=True)
        print_colored("=" * 60, "HEADER")
        
        config = STRATEGY_5_FILTER_CONFIG
        print_colored(f"Filter Enabled: {'✅ Yes' if config['ENABLED'] else '❌ No'}", "INFO")
        print_colored(f"Min Trend Strength: {config['MIN_TREND_STRENGTH']:.2f}", "INFO")
        print_colored(f"Debug Mode: {'✅ On' if config['DEBUG_MODE'] else '❌ Off'}", "INFO")
        print_colored(f"\nDescription: {config['DESCRIPTION']}", "INFO")
        
        print_colored("\n📈 Trend Strength Guide:", "HEADER")
        print_colored("  • 0.0 - 0.4: Very weak/sideways market", "WARNING")
        print_colored("  • 0.5: Neutral/sideways market", "WARNING") 
        print_colored("  • 0.6 - 0.7: Moderate trending market", "SUCCESS")
        print_colored("  • 0.8 - 0.9: Strong trending market", "SUCCESS")
        print_colored("  • 0.9+: Very strong trending market", "SUCCESS")
        
    def analyze_signals_file(self, signals_file_path):
        """Analyze a signals file to show how the filter would have performed"""
        try:
            print_colored(f"\n🔍 Analyzing signals file: {signals_file_path}", "HEADER", bold=True)
            
            with open(signals_file_path, 'r') as f:
                signals = json.load(f)
            
            if not signals:
                print_colored("❌ No signals found in file", "ERROR")
                return
                
            total_signals = len(signals)
            s5_signals = [s for s in signals if s.get('strategy_used') == 'S5']
            
            if not s5_signals:
                print_colored("❌ No Strategy 5 signals found in file", "ERROR")
                return
                
            print_colored(f"📊 Found {len(s5_signals)} Strategy 5 signals out of {total_signals} total signals", "INFO")
            
            # Analyze results
            wins = len([s for s in s5_signals if s.get('result') == 'win'])
            losses = len([s for s in s5_signals if s.get('result') == 'loss'])
            pending = len([s for s in s5_signals if s.get('result') == 'pending'])
            
            print_colored(f"\n📈 Strategy 5 Performance Analysis:", "HEADER")
            print_colored(f"  Wins: {wins}", "SUCCESS")
            print_colored(f"  Losses: {losses}", "ERROR")
            print_colored(f"  Pending: {pending}", "WARNING")
            
            if wins + losses > 0:
                win_rate = (wins / (wins + losses)) * 100
                print_colored(f"  Win Rate: {win_rate:.1f}%", "SUCCESS" if win_rate >= 60 else "WARNING")
            
            # Check market conditions
            sideways_signals = [s for s in s5_signals if s.get('market_analysis', {}).get('trend') == 'sideways']
            trending_signals = [s for s in s5_signals if s.get('market_analysis', {}).get('trend') != 'sideways']
            
            print_colored(f"\n🌊 Market Condition Analysis:", "HEADER")
            print_colored(f"  Sideways Market Signals: {len(sideways_signals)}", "WARNING")
            print_colored(f"  Trending Market Signals: {len(trending_signals)}", "SUCCESS")
            
            if sideways_signals:
                sideways_wins = len([s for s in sideways_signals if s.get('result') == 'win'])
                sideways_losses = len([s for s in sideways_signals if s.get('result') == 'loss'])
                
                if sideways_wins + sideways_losses > 0:
                    sideways_win_rate = (sideways_wins / (sideways_wins + sideways_losses)) * 100
                    print_colored(f"  Sideways Win Rate: {sideways_win_rate:.1f}%", "ERROR" if sideways_win_rate < 50 else "WARNING")
                    
                    print_colored(f"\n💡 Filter Impact Analysis:", "HEADER")
                    print_colored(f"  Signals that would be filtered out: {len(sideways_signals)}", "INFO")
                    print_colored(f"  Losses that would be prevented: {sideways_losses}", "SUCCESS")
                    print_colored(f"  Wins that would be missed: {sideways_wins}", "WARNING")
                    
                    net_improvement = sideways_losses - sideways_wins
                    if net_improvement > 0:
                        print_colored(f"  Net improvement: +{net_improvement} trades", "SUCCESS")
                    else:
                        print_colored(f"  Net impact: {net_improvement} trades", "WARNING")
            
        except FileNotFoundError:
            print_colored(f"❌ File not found: {signals_file_path}", "ERROR")
        except json.JSONDecodeError:
            print_colored(f"❌ Invalid JSON file: {signals_file_path}", "ERROR")
        except Exception as e:
            print_colored(f"❌ Error analyzing file: {str(e)}", "ERROR")
    
    def test_filter_with_threshold(self, threshold):
        """Test how changing the threshold would affect filtering"""
        if not 0.0 <= threshold <= 1.0:
            print_colored("❌ Threshold must be between 0.0 and 1.0", "ERROR")
            return
            
        print_colored(f"\n🧪 Testing Strategy 5 filter with threshold: {threshold:.2f}", "HEADER", bold=True)
        
        # Update the strategy engine threshold temporarily
        original_threshold = self.strategy_engine.strategy_5_min_trend_strength
        self.strategy_engine.strategy_5_min_trend_strength = threshold
        self.strategy_engine.set_debug_mode(True)
        
        print_colored(f"✅ Threshold updated from {original_threshold:.2f} to {threshold:.2f}", "SUCCESS")
        print_colored("🔧 Debug mode enabled for testing", "INFO")
        print_colored("\nNow run your trading analysis to see the filter in action!", "INFO")
        
    def interactive_menu(self):
        """Interactive menu for managing Strategy 5 filter"""
        while True:
            print_colored("\n🎛️  Strategy 5 Filter Manager", "HEADER", bold=True)
            print_colored("=" * 40, "HEADER")
            print_colored("1. Show current configuration", "INFO")
            print_colored("2. Analyze signals file", "INFO")
            print_colored("3. Test filter threshold", "INFO")
            print_colored("4. Enable/disable debug mode", "INFO")
            print_colored("5. Exit", "INFO")
            
            try:
                choice = input("\nSelect option (1-5): ").strip()
                
                if choice == '1':
                    self.show_current_config()
                    
                elif choice == '2':
                    file_path = input("Enter signals file path: ").strip()
                    if file_path:
                        self.analyze_signals_file(file_path)
                    
                elif choice == '3':
                    threshold_str = input("Enter new threshold (0.0-1.0): ").strip()
                    try:
                        threshold = float(threshold_str)
                        self.test_filter_with_threshold(threshold)
                    except ValueError:
                        print_colored("❌ Invalid threshold value", "ERROR")
                
                elif choice == '4':
                    current_debug = self.strategy_engine._debug_mode
                    self.strategy_engine.set_debug_mode(not current_debug)
                    
                elif choice == '5':
                    print_colored("👋 Goodbye!", "SUCCESS")
                    break
                    
                else:
                    print_colored("❌ Invalid option", "ERROR")
                    
            except KeyboardInterrupt:
                print_colored("\n👋 Goodbye!", "SUCCESS")
                break
            except Exception as e:
                print_colored(f"❌ Error: {str(e)}", "ERROR")

def main():
    """Main function"""
    manager = Strategy5FilterManager()
    
    # Show current config on startup
    manager.show_current_config()
    
    # Start interactive menu
    manager.interactive_menu()

if __name__ == "__main__":
    main()
