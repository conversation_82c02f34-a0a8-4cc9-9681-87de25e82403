#!/usr/bin/env python3
"""
Quotex Asset Mapper - Gets real asset names from Quotex API
Dynamically maps bot asset names to actual Quotex asset names
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from utils import print_colored

class QuotexAssetMapper:
    def __init__(self):
        """Initialize asset mapper"""
        self.asset_mapping = {}
        self.available_assets = []
        self.mapping_loaded = False
    
    async def load_asset_mapping(self, quotex_client) -> bool:
        """Load real asset names from Quotex browser interface"""
        try:
            if not quotex_client or not quotex_client.check_connect:
                print_colored("❌ Quotex client not connected", "ERROR")
                return False

            print_colored("🔍 Loading real asset names from Quotex browser interface...", "INFO")

            # Method 1: Scrape asset names from browser interface
            browser_assets = await self._scrape_assets_from_browser(quotex_client)

            # Method 2: Always use predefined mapping (more reliable)
            print_colored("🔧 Using predefined mapping with known Quotex formats", "INFO")
            self._create_predefined_mapping()

            # Merge browser assets with predefined mapping
            if browser_assets:
                print_colored(f"✅ Found {len(browser_assets)} additional assets from browser", "SUCCESS")
                self._merge_browser_assets(browser_assets)

            self.mapping_loaded = True
            return True

        except Exception as e:
            print_colored(f"❌ Error loading asset mapping: {e}", "ERROR")
            # Fallback to predefined mapping
            self._create_predefined_mapping()
            self.mapping_loaded = True
            return True
    
    def _create_mapping_from_assets(self, all_assets: List[List]):
        """Create mapping from get_all_asset_name result"""
        try:
            self.asset_mapping = {}
            
            for asset_info in all_assets:
                if len(asset_info) >= 2:
                    asset_id = asset_info[0]
                    asset_name = asset_info[1].strip()
                    
                    # Create bot name mapping
                    bot_name = self._create_bot_name(asset_name)
                    if bot_name:
                        self.asset_mapping[bot_name] = asset_name
                        print_colored(f"   {bot_name} → {asset_name}", "INFO")
            
            print_colored(f"✅ Created mapping for {len(self.asset_mapping)} assets", "SUCCESS")
            
        except Exception as e:
            print_colored(f"❌ Error creating mapping from assets: {e}", "ERROR")
    
    def _create_mapping_from_instruments(self, instruments: List):
        """Create mapping from get_instruments result"""
        try:
            self.asset_mapping = {}
            
            for instrument in instruments:
                if len(instrument) >= 2:
                    asset_id = instrument[0]
                    asset_name = instrument[1].strip()
                    
                    # Create bot name mapping
                    bot_name = self._create_bot_name(asset_name)
                    if bot_name:
                        self.asset_mapping[bot_name] = asset_name
                        print_colored(f"   {bot_name} → {asset_name}", "INFO")
            
            print_colored(f"✅ Created mapping for {len(self.asset_mapping)} instruments", "SUCCESS")
            
        except Exception as e:
            print_colored(f"❌ Error creating mapping from instruments: {e}", "ERROR")
    
    def _create_mapping_from_codes(self, asset_codes: Dict):
        """Create mapping from get_all_assets result"""
        try:
            self.asset_mapping = {}
            
            for asset_name, asset_code in asset_codes.items():
                asset_name = asset_name.strip()
                
                # Create bot name mapping
                bot_name = self._create_bot_name(asset_name)
                if bot_name:
                    self.asset_mapping[bot_name] = asset_name
                    print_colored(f"   {bot_name} → {asset_name}", "INFO")
            
            print_colored(f"✅ Created mapping for {len(self.asset_mapping)} asset codes", "SUCCESS")
            
        except Exception as e:
            print_colored(f"❌ Error creating mapping from codes: {e}", "ERROR")

    async def _scrape_assets_from_browser(self, quotex_client) -> List[str]:
        """Scrape asset names from Quotex browser interface"""
        try:
            if not hasattr(quotex_client, 'page') or not quotex_client.page:
                print_colored("❌ No browser page available", "ERROR")
                return []

            page = quotex_client.page

            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=10000)

            # Look for asset selectors in the trading interface
            asset_selectors = [
                '.asset-list .asset-item',
                '.pairs-list .pair-item',
                '.trading-assets .asset',
                '.currency-list .currency',
                '[data-asset]',
                '.tab__label',  # This is the format you showed: USD/PKR (OTC)
                '.asset-selector option',
                '.pair-selector option'
            ]

            found_assets = []

            for selector in asset_selectors:
                try:
                    elements = await page.query_selector_all(selector)

                    for element in elements:
                        try:
                            # Try to get text content
                            text = await element.text_content()
                            if text:
                                text = text.strip()
                                if self._is_valid_asset_name(text):
                                    found_assets.append(text)
                                    print_colored(f"   Found: {text}", "INFO")

                            # Try to get data-asset attribute
                            data_asset = await element.get_attribute('data-asset')
                            if data_asset:
                                data_asset = data_asset.strip()
                                if self._is_valid_asset_name(data_asset):
                                    found_assets.append(data_asset)
                                    print_colored(f"   Found: {data_asset}", "INFO")

                        except Exception:
                            continue

                except Exception:
                    continue

            # Remove duplicates and return
            unique_assets = list(set(found_assets))
            print_colored(f"✅ Scraped {len(unique_assets)} unique assets from browser", "SUCCESS")
            return unique_assets

        except Exception as e:
            print_colored(f"❌ Error scraping assets from browser: {e}", "ERROR")
            return []

    def _is_valid_asset_name(self, text: str) -> bool:
        """Check if text looks like a valid asset name"""
        if not text or len(text) < 3:
            return False

        # Check for common asset patterns
        patterns = [
            r'^[A-Z]{6}_otc$',  # EURUSD_otc
            r'^[A-Z]{3}/[A-Z]{3}\s*\(OTC\)$',  # USD/PKR (OTC)
            r'^[A-Z]{6}$',  # EURUSD
            r'^[A-Z]{3}/[A-Z]{3}$',  # USD/PKR
            r'^X[A-Z]{2}USD$',  # XAUUSD
            r'^[A-Z]{4,6}$',  # AAPL, GOOGL
        ]

        import re
        for pattern in patterns:
            if re.match(pattern, text):
                return True

        return False

    def _create_mapping_from_browser_assets(self, browser_assets: List[str]):
        """Create mapping from browser-scraped assets"""
        try:
            self.asset_mapping = {}

            for asset_name in browser_assets:
                asset_name = asset_name.strip()

                # Create bot name mapping
                bot_name = self._create_bot_name(asset_name)
                if bot_name:
                    self.asset_mapping[bot_name] = asset_name
                    print_colored(f"   {bot_name} → {asset_name}", "INFO")

            print_colored(f"✅ Created mapping for {len(self.asset_mapping)} browser assets", "SUCCESS")

        except Exception as e:
            print_colored(f"❌ Error creating mapping from browser assets: {e}", "ERROR")

    def _create_predefined_mapping(self):
        """Create predefined mapping based on known Quotex asset formats"""
        try:
            # Based on your example: USD/PKR (OTC)
            predefined_assets = {
                # Major Currency Pairs (Quotex format)
                "EURUSD_otc": "EUR/USD (OTC)",
                "GBPUSD_otc": "GBP/USD (OTC)",
                "USDJPY_otc": "USD/JPY (OTC)",
                "AUDUSD_otc": "AUD/USD (OTC)",
                "USDCAD_otc": "USD/CAD (OTC)",
                "USDCHF_otc": "USD/CHF (OTC)",

                # Cross Currency Pairs
                "EURGBP_otc": "EUR/GBP (OTC)",
                "EURJPY_otc": "EUR/JPY (OTC)",
                "GBPJPY_otc": "GBP/JPY (OTC)",
                "AUDJPY_otc": "AUD/JPY (OTC)",
                "CADJPY_otc": "CAD/JPY (OTC)",

                # Exotic Currency Pairs (as you showed)
                "USDBDT_otc": "USD/BDT (OTC)",  # Bangladesh Taka
                "USDARS_otc": "USD/ARS (OTC)",  # Argentine Peso
                "USDBRL_otc": "USD/BRL (OTC)",  # Brazilian Real
                "USDCLP_otc": "USD/CLP (OTC)",  # Chilean Peso
                "USDCOP_otc": "USD/COP (OTC)",  # Colombian Peso
                "USDEGP_otc": "USD/EGP (OTC)",  # Egyptian Pound
                "USDILS_otc": "USD/ILS (OTC)",  # Israeli Shekel
                "USDINR_otc": "USD/INR (OTC)",  # Indian Rupee
                "USDKRW_otc": "USD/KRW (OTC)",  # South Korean Won
                "USDMXN_otc": "USD/MXN (OTC)",  # Mexican Peso
                "USDNGN_otc": "USD/NGN (OTC)",  # Nigerian Naira
                "USDPKR_otc": "USD/PKR (OTC)",  # Pakistani Rupee (your example!)
                "USDTHB_otc": "USD/THB (OTC)",  # Thai Baht
                "USDTRY_otc": "USD/TRY (OTC)",  # Turkish Lira
                "USDVND_otc": "USD/VND (OTC)",  # Vietnamese Dong
                "USDZAR_otc": "USD/ZAR (OTC)",  # South African Rand

                # Precious Metals
                "XAUUSD_otc": "XAU/USD (OTC)",  # Gold
                "XAGUSD_otc": "XAG/USD (OTC)",  # Silver

                # Major Stocks
                "AAPL_otc": "AAPL (OTC)",
                "MSFT_otc": "MSFT (OTC)",
                "GOOGL_otc": "GOOGL (OTC)",
                "AMZN_otc": "AMZN (OTC)",
                "TSLA_otc": "TSLA (OTC)",
                "NVDA_otc": "NVDA (OTC)",
            }

            self.asset_mapping = predefined_assets
            print_colored(f"✅ Created predefined mapping for {len(self.asset_mapping)} assets", "SUCCESS")

            # Show some examples
            print_colored("📋 Sample mappings:", "INFO")
            for bot_name, quotex_name in list(predefined_assets.items())[:5]:
                print_colored(f"   {bot_name} → {quotex_name}", "INFO")
            print_colored(f"   ... and {len(predefined_assets) - 5} more", "INFO")

        except Exception as e:
            print_colored(f"❌ Error creating predefined mapping: {e}", "ERROR")

    def _merge_browser_assets(self, browser_assets: List[str]):
        """Merge browser-scraped assets with predefined mapping"""
        try:
            for asset_name in browser_assets:
                asset_name = asset_name.strip()

                # Create bot name mapping
                bot_name = self._create_bot_name(asset_name)
                if bot_name and bot_name not in self.asset_mapping:
                    self.asset_mapping[bot_name] = asset_name
                    print_colored(f"   Added from browser: {bot_name} → {asset_name}", "INFO")

            print_colored(f"✅ Merged browser assets, total: {len(self.asset_mapping)}", "SUCCESS")

        except Exception as e:
            print_colored(f"❌ Error merging browser assets: {e}", "ERROR")
    
    def _create_bot_name(self, quotex_asset_name: str) -> Optional[str]:
        """Create bot asset name from Quotex asset name"""
        try:
            # Clean the asset name
            clean_name = quotex_asset_name.strip()
            
            # Handle different formats
            if "(OTC)" in clean_name:
                # Format: "USD/PKR (OTC)" -> "USDPKR_otc"
                base_name = clean_name.replace("(OTC)", "").strip()
                if "/" in base_name:
                    # "USD/PKR" -> "USDPKR"
                    base_name = base_name.replace("/", "")
                return f"{base_name}_otc"
            
            elif "_otc" in clean_name.lower():
                # Already has _otc suffix
                return clean_name.lower()
            
            elif any(symbol in clean_name for symbol in ["/", "-"]):
                # Format: "USD/PKR" or "USD-PKR" -> "USDPKR_otc"
                base_name = clean_name.replace("/", "").replace("-", "")
                return f"{base_name}_otc"
            
            else:
                # Simple format: "EURUSD" -> "EURUSD_otc"
                return f"{clean_name}_otc"
            
        except Exception as e:
            print_colored(f"❌ Error creating bot name for {quotex_asset_name}: {e}", "ERROR")
            return None
    
    def get_quotex_asset_name(self, bot_asset_name: str) -> Optional[str]:
        """Get the real Quotex asset name for a bot asset name"""
        if not self.mapping_loaded:
            print_colored(f"⚠️ Asset mapping not loaded for {bot_asset_name}", "WARNING")
            return None
        
        # Direct mapping
        if bot_asset_name in self.asset_mapping:
            return self.asset_mapping[bot_asset_name]
        
        # Try variations
        variations = [
            bot_asset_name.upper(),
            bot_asset_name.lower(),
            bot_asset_name.replace("_otc", ""),
            bot_asset_name.replace("_otc", "").upper(),
        ]
        
        for variation in variations:
            if variation in self.asset_mapping:
                return self.asset_mapping[variation]
        
        # Try to find by partial match
        base_name = bot_asset_name.replace("_otc", "")
        for bot_name, quotex_name in self.asset_mapping.items():
            if base_name.upper() in quotex_name.upper():
                return quotex_name
        
        print_colored(f"❌ No mapping found for {bot_asset_name}", "ERROR")
        return None
    
    def get_available_bot_assets(self) -> List[str]:
        """Get list of available bot asset names"""
        return list(self.asset_mapping.keys())
    
    def get_available_quotex_assets(self) -> List[str]:
        """Get list of available Quotex asset names"""
        return list(self.asset_mapping.values())
    
    def print_mapping_summary(self):
        """Print summary of asset mapping"""
        if not self.mapping_loaded:
            print_colored("❌ Asset mapping not loaded", "ERROR")
            return
        
        print_colored("\n📊 QUOTEX ASSET MAPPING SUMMARY", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
        
        # Group by type
        currency_pairs = []
        stocks = []
        metals = []
        others = []
        
        for bot_name, quotex_name in self.asset_mapping.items():
            if any(curr in bot_name.upper() for curr in ["USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF"]):
                currency_pairs.append((bot_name, quotex_name))
            elif any(stock in bot_name.upper() for stock in ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]):
                stocks.append((bot_name, quotex_name))
            elif any(metal in bot_name.upper() for metal in ["XAU", "XAG", "GOLD", "SILVER"]):
                metals.append((bot_name, quotex_name))
            else:
                others.append((bot_name, quotex_name))
        
        if currency_pairs:
            print_colored(f"\n💱 CURRENCY PAIRS ({len(currency_pairs)}):", "INFO", bold=True)
            for bot_name, quotex_name in sorted(currency_pairs):
                print_colored(f"   {bot_name:<20} → {quotex_name}", "SUCCESS")
        
        if stocks:
            print_colored(f"\n📈 STOCKS ({len(stocks)}):", "INFO", bold=True)
            for bot_name, quotex_name in sorted(stocks):
                print_colored(f"   {bot_name:<20} → {quotex_name}", "SUCCESS")
        
        if metals:
            print_colored(f"\n🥇 PRECIOUS METALS ({len(metals)}):", "INFO", bold=True)
            for bot_name, quotex_name in sorted(metals):
                print_colored(f"   {bot_name:<20} → {quotex_name}", "SUCCESS")
        
        if others:
            print_colored(f"\n🔧 OTHER ASSETS ({len(others)}):", "INFO", bold=True)
            for bot_name, quotex_name in sorted(others):
                print_colored(f"   {bot_name:<20} → {quotex_name}", "SUCCESS")
        
        print_colored(f"\n✅ Total mapped assets: {len(self.asset_mapping)}", "SUCCESS", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
    
    async def check_asset_availability(self, quotex_client, bot_asset_name: str) -> Tuple[bool, Optional[str]]:
        """Check if an asset is available for trading"""
        try:
            quotex_name = self.get_quotex_asset_name(bot_asset_name)
            if not quotex_name:
                return False, None

            # For browser-based client, use simple check
            if hasattr(quotex_client, 'check_asset_open'):
                try:
                    # Browser-based client returns tuple directly
                    result = quotex_client.check_asset_open(quotex_name)
                    if isinstance(result, tuple) and len(result) >= 3:
                        asset_name, exists, is_open = result
                        return bool(exists and is_open), quotex_name
                except Exception as e:
                    print_colored(f"⚠️ Asset check failed for {bot_asset_name}: {e}", "WARNING")

            # Assume OTC assets are available (they usually are 24/7)
            if "_otc" in bot_asset_name or "(OTC)" in quotex_name:
                return True, quotex_name

            # For other assets, assume available during market hours
            return True, quotex_name

        except Exception as e:
            print_colored(f"❌ Error checking asset availability for {bot_asset_name}: {e}", "ERROR")
            return False, None

# Global asset mapper instance
quotex_asset_mapper = QuotexAssetMapper()

# Convenience functions
async def load_quotex_assets(quotex_client) -> bool:
    """Load asset mapping from Quotex"""
    return await quotex_asset_mapper.load_asset_mapping(quotex_client)

def get_real_quotex_asset_name(bot_asset_name: str) -> Optional[str]:
    """Get real Quotex asset name for bot asset name"""
    return quotex_asset_mapper.get_quotex_asset_name(bot_asset_name)

def get_available_assets() -> List[str]:
    """Get list of available bot asset names"""
    return quotex_asset_mapper.get_available_bot_assets()

def print_asset_mapping():
    """Print asset mapping summary"""
    quotex_asset_mapper.print_mapping_summary()

async def check_asset_status(quotex_client, bot_asset_name: str) -> Tuple[bool, Optional[str]]:
    """Check if asset is available for trading"""
    return await quotex_asset_mapper.check_asset_availability(quotex_client, bot_asset_name)
